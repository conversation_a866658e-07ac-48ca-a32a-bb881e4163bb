#pragma once

#include "common/Utils.h"

namespace koala {
class IKTcpClient {
public:
  using Ptr = std::shared_ptr<IKTcpClient>;
  virtual ~IKTcpClient() = default;
  virtual int setWithTLS(const char *crtFile = nullptr,
                         const char *keyFile = nullptr,
                         const char *caFile = nullptr,
                         const char *caPath = nullptr,
                         int16_t verifyPeer = 0) = 0;
  virtual void setReconnect(uint32_t minDelay = 1000, uint32_t maxDelay = 10000,
                            uint32_t delayPolicy = 2) = 0;
  virtual void setKeepaliveTimeout(int32_t ms) = 0;
  virtual void startConnect(const char *host, uint16_t port) = 0;
  virtual void stop() = 0;
  virtual int send(const void *data, int size) = 0;
};

class ITcpClientSink {
public:
  virtual ~ITcpClientSink() = default;
  virtual void onTcpClientConnected(IKTcpClient::Ptr client,
                                    const char *peerAddr) = 0;
  virtual void onTcpClientMessage(IKTcpClient::Ptr client, const char *peerAddr,
                                  const void *buf, int size) = 0;
  virtual void onTcpClientDisconnected(IKTcpClient::Ptr client,
                                       const char *peerAddr) = 0;
};

KEXTERN KOALA_API IKTcpClient::Ptr
KCreateTcpClient(ITcpClientSink *sink = nullptr);

} // namespace koala
