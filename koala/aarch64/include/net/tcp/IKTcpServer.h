#pragma once
#include "common/Utils.h"

namespace koala {

class IKTcpServer {
public:
  using Ptr = std::shared_ptr<IKTcpServer>;
  virtual ~IKTcpServer() = default;
  virtual int setWithTLS(const char *crtFile = nullptr,
                         const char *keyFile = nullptr,
                         const char *caFile = nullptr,
                         const char *caPath = nullptr,
                         int16_t verifyPeer = 0) = 0;
  virtual void setMaxConnectionNum(uint32_t num) = 0;
  virtual void setThreadNum(int num) = 0;
  virtual void setLoadBalance(kLoadBlance lb) = 0;
  virtual void setKeepaliveTimeout(int ms) = 0;
  virtual void setUnpack(KUnpackSetting &setting) = 0;
  virtual void shutdown(uint32_t sessionId) = 0;
  virtual void start(bool waitThreadsStarted = true) = 0;
  virtual void stop(bool waitThreadsStopped = true) = 0;
  virtual int broadcast(const void *data, int size) = 0;
  virtual int send(uint32_t sessionId, const void *data, int size) = 0;
};

class IKTcpServerSink {
public:
  virtual ~IKTcpServerSink() = default;
  virtual void onTcpSessionConnected(IKTcpServer::Ptr srv, uint32_t sessionId,
                                     const char *peerAddr) = 0;
  virtual void onTcpSessionMessage(IKTcpServer::Ptr srv, uint32_t sessionId,
                                   const char *peerAddr, const void *data,
                                   size_t size) = 0;
  virtual void onTcpSessionDisconnected(IKTcpServer::Ptr srv,
                                        uint32_t sessionId,
                                        const char *peerAddr) = 0;
};

KEXTERN KOALA_API IKTcpServer::Ptr
KCreateTcpServer(uint16_t port, IKTcpServerSink *sink = nullptr);
} // namespace koala