#pragma once

#include "IKHttpRequest.h"
#include "IKHttpResponse.h"
#include "common/Utils.h"
#include <functional>

namespace koala {
using HttpResponseCallback = std::function<void(const IKHttpResponse::Ptr &)>;

class IKHttpClient {
public:
  using Ptr = std::shared_ptr<IKHttpClient>;
  virtual ~IKHttpClient() = default;
  virtual int setWithTLS(const char *crtFile = nullptr,
                         const char *keyFile = nullptr,
                         const char *caFile = nullptr,
                         const char *caPath = nullptr,
                         int16_t verifyPeer = 0) = 0;
  virtual int setTimeout(int timeout) = 0;
  virtual int clearHeaders() = 0;
  virtual int setHeader(const char *key, const char *value) = 0;
  virtual int delHeader(const char *key) = 0;
  virtual const char *getHeader(const char *key) = 0;
  virtual int setHttpProxy(const char *host, int port) = 0;
  virtual int setHttpsProxy(const char *host, int port) = 0;
  virtual int addNoProxy(const char *host) = 0;
  virtual int send(IKHttpRequest::Ptr req, IKHttpResponse::Ptr resp) = 0;
  virtual int sendAsync(IKHttpRequest::Ptr req,
                        HttpResponseCallback resp_cb = nullptr) = 0;
};

KEXTERN KOALA_API IKHttpClient::Ptr
KCreateHttpClient(const char *host = nullptr, int port = 80, int https = 0);

KEXTERN KOALA_API IKHttpResponse::Ptr
KHttpRequestGet(const std::string &url,
                const KHttpParams &params = kDefaultParams,
                const KHttpHeaders &headers = kDefaultHeaders);

KEXTERN KOALA_API IKHttpResponse::Ptr
KHttpsRequestGet(const std::string &url, const std::string &crtFile,
                 const std::string &keyFile,
                 const KHttpParams &params = kDefaultParams,
                 const KHttpHeaders &headers = kDefaultHeaders);

KEXTERN KOALA_API IKHttpResponse::Ptr
KHttpRequestPost(const std::string &url, const std::string &body,
                 const KHttpParams &params = kDefaultParams,
                 const KHttpHeaders &headers = kDefaultHeaders);

KEXTERN KOALA_API IKHttpResponse::Ptr
KHttpsRequestPost(const std::string &url, const std::string &body,
                  const std::string &crtFile, const std::string &keyFile,
                  const KHttpParams &params = kDefaultParams,
                  const KHttpHeaders &headers = kDefaultHeaders);
} // namespace koala