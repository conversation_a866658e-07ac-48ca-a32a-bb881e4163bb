#pragma once

#include "common/Utils.h"

namespace koala {
class IKHttpRequest {
public:
  using Ptr = std::shared_ptr<IKHttpRequest>;
  virtual ~IKHttpRequest() = default;
  virtual void reset() = 0;
  virtual std::string getPath() = 0;
  virtual std::string getFullPath() = 0;
  virtual void setHost(const char *host, int port) = 0;
  virtual std::string getHost() = 0;
  virtual void setUrlEncoded(const char *key, const std::string &value) = 0;
  virtual std::string getUrlEncoded(const char *key,
                                    const std::string &defValue = "") = 0;
  virtual void setHeader(const char *key, const std::string &value) = 0;
  virtual std::string getHeader(const char *key,
                                const std::string &defValue = "") = 0;
  virtual void setParam(const char *key, const std::string &value) = 0;
  virtual std::string getParam(const char *key,
                               const std::string &defValue = "") = 0;
  virtual bool isChunked() = 0;
  virtual bool isKeepalive() = 0;
  virtual bool isUpgrade() = 0;
  virtual void setBody(const std::string &body) = 0;
  virtual const std::string &getBody() = 0;
  virtual void dumpHeaders(std::string &headers) = 0;
  virtual void dumpBody(std::string &body) = 0;
  virtual std::string getString(const char *key,
                                const std::string &defValue = "") = 0;
  virtual const std::string &getJson() = 0;
  virtual std::string getFormData(const char *name,
                                  const std::string &defValue = "") = 0;
  virtual void setFormData(const char *name, const std::string &data) = 0;
  virtual bool getBool(const char *key, bool defValue = false) = 0;
  virtual int64_t getInt(const char *key, int64_t defValue = 0) = 0;
  virtual double getFloat(const char *key, double defValue = 0.0) = 0;
  virtual void setRange(long from = 0, long to = -1) = 0;
  virtual bool getRange(long &from, long &to) = 0;
  virtual void setAuth(const std::string &auth) = 0;
  virtual void setBasicAuth(const std::string &username,
                            const std::string &password) = 0;
  virtual void setBearerTokenAuth(const std::string &token) = 0;
  virtual void setTimeout(int sec) = 0;
  virtual void setConnectTimeout(int sec) = 0;
  virtual void allowRedirect(bool on = true) = 0;
  virtual void setProxy(const char *host, int port) = 0;
  virtual bool isProxy() = 0;
  virtual void setMethod(const char *method) = 0;
  virtual const char *getMethod() = 0;
  virtual bool isHttps() = 0;
  virtual void setUrl(const char *url) = 0;
  virtual const std::string &getUrl() = 0;
  virtual int sendString(const std::string &message) = 0;
  virtual int sendData(void *data, int len, bool nocopy = true) = 0;
  virtual int sendFile(const char *filepath) = 0;
  virtual int sendJson(const std::string &json) = 0;
};

KEXTERN KOALA_API IKHttpRequest::Ptr KCreateHttpRequest();

} // namespace koala