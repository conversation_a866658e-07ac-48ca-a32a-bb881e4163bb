#pragma once

#include "IKHttp.h"
#include "IKHttpResponse.h"
#include "IKWebSocketChannel.h"
#include "common/Utils.h"
#include "event/IKEventLoop.h"

namespace koala {
class IKWebSocketClientSink {
public:
  virtual ~IKWebSocketClientSink() = default;
  virtual void onWSClientOpen(const IKHttpResponse::Ptr &resp) = 0;
  virtual void onWSClientMessage(const IKHttpResponse::Ptr &resp,
                                 const std::string &msg) = 0;
  virtual void onWSClientClose(const IKHttpResponse::Ptr &resp) = 0;
};

class IKWebSocketClient {
public:
  using Ptr = std::shared_ptr<IKWebSocketClient>;
  virtual ~IKWebSocketClient() = default;
  virtual int setWithTLS(const char *crtFile = nullptr,
                         const char *keyFile = nullptr,
                         const char *caFile = nullptr,
                         const char *caPath = nullptr,
                         int16_t verifyPeer = 0) = 0;
  virtual void setSink(IKWebSocketClientSink *sink) = 0;
  virtual void setPingInterval(int ms) = 0;
  virtual void setReconnect(uint32_t minDelay = 1000, uint32_t maxDelay = 10000,
                            uint32_t delayPolicy = 2) = 0;
  virtual int connect(const char *url,
                      KHttpHeaders &headers = kDefaultHeaders) = 0;
  virtual int send(const std::string &msg) = 0;
  virtual int send(const char *buf, int len, KWebSocketCode code) = 0;
  virtual int close() = 0;
};

KEXTERN KOALA_API IKWebSocketClient::Ptr
KCreateWebSocketClient(IKEventLoop::Ptr loop);

} // namespace koala