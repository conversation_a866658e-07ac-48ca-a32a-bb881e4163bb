#pragma once

#include "common/Utils.h"
#include <cstring>
#include <map>
#include <string>

namespace {
#define KHTTP_STATUS_MAP(XX)                                                   \
  XX(0, NEXT, Next)                                                            \
  XX(100, CONTINUE, Continue)                                                  \
  XX(101, SWITCHING_PROTOCOLS, Switching Protocols)                            \
  XX(102, PROCESSING, Processing)                                              \
  XX(200, OK, OK)                                                              \
  XX(201, CREATED, Created)                                                    \
  XX(202, ACCEPTED, Accepted)                                                  \
  XX(203, NON_AUTHORITATIVE_INFORMATION, Non - Authoritative Information)      \
  XX(204, NO_CONTENT, No Content)                                              \
  XX(205, RESET_CONTENT, Reset Content)                                        \
  XX(206, PARTIAL_CONTENT, Partial Content)                                    \
  XX(207, MULTI_STATUS, Multi - Status)                                        \
  XX(208, ALREADY_REPORTED, Already Reported)                                  \
  XX(226, IM_USED, IM Used)                                                    \
  XX(300, MULTIPLE_CHOICES, Multiple Choices)                                  \
  XX(301, MOVED_PERMANENTLY, Moved Permanently)                                \
  XX(302, FOUND, Found)                                                        \
  XX(303, SEE_OTHER, See Other)                                                \
  XX(304, NOT_MODIFIED, Not Modified)                                          \
  XX(305, USE_PROXY, Use Proxy)                                                \
  XX(307, TEMPORARY_REDIRECT, Temporary Redirect)                              \
  XX(308, PERMANENT_REDIRECT, Permanent Redirect)                              \
  XX(400, BAD_REQUEST, Bad Request)                                            \
  XX(401, UNAUTHORIZED, Unauthorized)                                          \
  XX(402, PAYMENT_REQUIRED, Payment Required)                                  \
  XX(403, FORBIDDEN, Forbidden)                                                \
  XX(404, NOT_FOUND, Not Found)                                                \
  XX(405, METHOD_NOT_ALLOWED, Method Not Allowed)                              \
  XX(406, NOT_ACCEPTABLE, Not Acceptable)                                      \
  XX(407, PROXY_AUTHENTICATION_REQUIRED, Proxy Authentication Required)        \
  XX(408, REQUEST_TIMEOUT, Request Timeout)                                    \
  XX(409, CONFLICT, Conflict)                                                  \
  XX(410, GONE, Gone)                                                          \
  XX(411, LENGTH_REQUIRED, Length Required)                                    \
  XX(412, PRECONDITION_FAILED, Precondition Failed)                            \
  XX(413, PAYLOAD_TOO_LARGE, Payload Too Large)                                \
  XX(414, URI_TOO_LONG, URI Too Long)                                          \
  XX(415, UNSUPPORTED_MEDIA_TYPE, Unsupported Media Type)                      \
  XX(416, RANGE_NOT_SATISFIABLE, Range Not Satisfiable)                        \
  XX(417, EXPECTATION_FAILED, Expectation Failed)                              \
  XX(421, MISDIRECTED_REQUEST, Misdirected Request)                            \
  XX(422, UNPROCESSABLE_ENTITY, Unprocessable Entity)                          \
  XX(423, LOCKED, Locked)                                                      \
  XX(424, FAILED_DEPENDENCY, Failed Dependency)                                \
  XX(426, UPGRADE_REQUIRED, Upgrade Required)                                  \
  XX(428, PRECONDITION_REQUIRED, Precondition Required)                        \
  XX(429, TOO_MANY_REQUESTS, Too Many Requests)                                \
  XX(431, REQUEST_HEADER_FIELDS_TOO_LARGE, Request Header Fields Too Large)    \
  XX(451, UNAVAILABLE_FOR_LEGAL_REASONS, Unavailable For Legal Reasons)        \
  XX(500, INTERNAL_SERVER_ERROR, Internal Server Error)                        \
  XX(501, NOT_IMPLEMENTED, Not Implemented)                                    \
  XX(502, BAD_GATEWAY, Bad Gateway)                                            \
  XX(503, SERVICE_UNAVAILABLE, Service Unavailable)                            \
  XX(504, GATEWAY_TIMEOUT, Gateway Timeout)                                    \
  XX(505, HTTP_VERSION_NOT_SUPPORTED, HTTP Version Not Supported)              \
  XX(506, VARIANT_ALSO_NEGOTIATES, Variant Also Negotiates)                    \
  XX(507, INSUFFICIENT_STORAGE, Insufficient Storage)                          \
  XX(508, LOOP_DETECTED, Loop Detected)                                        \
  XX(510, NOT_EXTENDED, Not Extended)                                          \
  XX(511, NETWORK_AUTHENTICATION_REQUIRED, Network Authentication Required)

enum KHttpStatus {
#define XX(num, name, string) KHTTP_STATUS_##name = num,
  KHTTP_STATUS_MAP(XX)
#undef XX
      KHTTP_CUSTOM_STATUS
};

#define KHTTP_METHOD_MAP(XX)                                                   \
  XX(0, DELETE, DELETE)                                                        \
  XX(1, GET, GET)                                                              \
  XX(2, HEAD, HEAD)                                                            \
  XX(3, POST, POST)                                                            \
  XX(4, PUT, PUT)                                                              \
  /* pathological */                                                           \
  XX(5, CONNECT, CONNECT)                                                      \
  XX(6, OPTIONS, OPTIONS)                                                      \
  XX(7, TRACE, TRACE)                                                          \
  /* WebDAV */                                                                 \
  XX(8, COPY, COPY)                                                            \
  XX(9, LOCK, LOCK)                                                            \
  XX(10, MKCOL, MKCOL)                                                         \
  XX(11, MOVE, MOVE)                                                           \
  XX(12, PROPFIND, PROPFIND)                                                   \
  XX(13, PROPPATCH, PROPPATCH)                                                 \
  XX(14, SEARCH, SEARCH)                                                       \
  XX(15, UNLOCK, UNLOCK)                                                       \
  XX(16, BIND, BIND)                                                           \
  XX(17, REBIND, REBIND)                                                       \
  XX(18, UNBIND, UNBIND)                                                       \
  XX(19, ACL, ACL)                                                             \
  /* subversion */                                                             \
  XX(20, REPORT, REPORT)                                                       \
  XX(21, MKACTIVITY, MKACTIVITY)                                               \
  XX(22, CHECKOUT, CHECKOUT)                                                   \
  XX(23, MERGE, MERGE)                                                         \
  /* upnp */                                                                   \
  XX(24, MSEARCH, M - SEARCH)                                                  \
  XX(25, NOTIFY, NOTIFY)                                                       \
  XX(26, SUBSCRIBE, SUBSCRIBE)                                                 \
  XX(27, UNSUBSCRIBE, UNSUBSCRIBE)                                             \
  /* RFC-5789 */                                                               \
  XX(28, PATCH, PATCH)                                                         \
  XX(29, PURGE, PURGE)                                                         \
  /* CalDAV */                                                                 \
  XX(30, MKCALENDAR, MKCALENDAR)                                               \
  /* RFC-2068, section 19.6.1.2 */                                             \
  XX(31, LINK, LINK)                                                           \
  XX(32, UNLINK, UNLINK)                                                       \
  /* icecast */                                                                \
  XX(33, SOURCE, SOURCE)

// HTTP_##name
enum KHttpMethod {
#define XX(num, name, string) KHTTP_##name = num,
  KHTTP_METHOD_MAP(XX)
#undef XX
      KHTTP_CUSTOM_METHOD
};

#define KMIME_TYPE_TEXT_MAP(XX)                                                \
  XX(KTEXT_PLAIN, text / plain, txt)                                           \
  XX(KTEXT_HTML, text / html, html)                                            \
  XX(KTEXT_CSS, text / css, css)                                               \
  XX(KTEXT_CSV, text / csv, csv)                                               \
  XX(KTEXT_MARKDOWN, text / markdown, md)                                      \
  XX(KTEXT_EVENT_STREAM, text / event - stream, sse)

#define KMIME_TYPE_APPLICATION_MAP(XX)                                         \
  XX(KAPPLICATION_JAVASCRIPT, application / javascript, js)                    \
  XX(KAPPLICATION_JSON, application / json, json)                              \
  XX(KAPPLICATION_XML, application / xml, xml)                                 \
  XX(KAPPLICATION_URLENCODED, application / x - www - form - urlencoded, kv)   \
  XX(KAPPLICATION_OCTET_STREAM, application / octet - stream, bin)             \
  XX(KAPPLICATION_ZIP, application / zip, zip)                                 \
  XX(KAPPLICATION_GZIP, application / gzip, gzip)                              \
  XX(KAPPLICATION_7Z, application / x - 7z - compressed, 7z)                   \
  XX(KAPPLICATION_RAR, application / x - rar - compressed, rar)                \
  XX(KAPPLICATION_PDF, application / pdf, pdf)                                 \
  XX(KAPPLICATION_RTF, application / rtf, rtf)                                 \
  XX(KAPPLICATION_GRPC, application / grpc, grpc)                              \
  XX(KAPPLICATION_WASM, application / wasm, wasm)                              \
  XX(KAPPLICATION_JAR, application / java - archive, jar)                      \
  XX(KAPPLICATION_XHTML, application / xhtml + xml, xhtml)                     \
  XX(KAPPLICATION_ATOM, application / atom + xml, atom)                        \
  XX(KAPPLICATION_RSS, application / rss + xml, rss)                           \
  XX(KAPPLICATION_WORD, application / msword, doc)                             \
  XX(KAPPLICATION_EXCEL, application / vnd.ms - excel, xls)                    \
  XX(KAPPLICATION_PPT, application / vnd.ms - powerpoint, ppt)                 \
  XX(KAPPLICATION_EOT, application / vnd.ms - fontobject, eot)                 \
  XX(KAPPLICATION_M3U8, application / vnd.apple.mpegurl, m3u8)                 \
  XX(KAPPLICATION_DOCX,                                                        \
     application / vnd.openxmlformats -                                        \
         officedocument.wordprocessingml.document,                             \
     docx)                                                                     \
  XX(KAPPLICATION_XLSX,                                                        \
     application / vnd.openxmlformats - officedocument.spreadsheetml.sheet,    \
     xlsx)                                                                     \
  XX(KAPPLICATION_PPTX,                                                        \
     application / vnd.openxmlformats -                                        \
         officedocument.presentationml.presentation,                           \
     pptx)

#define KMIME_TYPE_MULTIPART_MAP(XX)                                           \
  XX(KMULTIPART_FORM_DATA, multipart / form - data, mp)

#define KMIME_TYPE_IMAGE_MAP(XX)                                               \
  XX(KIMAGE_JPEG, image / jpeg, jpg)                                           \
  XX(KIMAGE_PNG, image / png, png)                                             \
  XX(KIMAGE_GIF, image / gif, gif)                                             \
  XX(KIMAGE_ICO, image / x - icon, ico)                                        \
  XX(KIMAGE_BMP, image / x - ms - bmp, bmp)                                    \
  XX(KIMAGE_SVG, image / svg + xml, svg)                                       \
  XX(KIMAGE_TIFF, image / tiff, tiff)                                          \
  XX(KIMAGE_WEBP, image / webp, webp)

#define KMIME_TYPE_VIDEO_MAP(XX)                                               \
  XX(KVIDEO_MP4, video / mp4, mp4)                                             \
  XX(KVIDEO_FLV, video / x - flv, flv)                                         \
  XX(KVIDEO_M4V, video / x - m4v, m4v)                                         \
  XX(KVIDEO_MNG, video / x - mng, mng)                                         \
  XX(KVIDEO_TS, video / mp2t, ts)                                              \
  XX(KVIDEO_MPEG, video / mpeg, mpeg)                                          \
  XX(KVIDEO_WEBM, video / webm, webm)                                          \
  XX(KVIDEO_MOV, video / quicktime, mov)                                       \
  XX(KVIDEO_3GPP, video / 3gpp, 3gpp)                                          \
  XX(KVIDEO_AVI, video / x - msvideo, avi)                                     \
  XX(KVIDEO_WMV, video / x - ms - wmv, wmv)                                    \
  XX(KVIDEO_ASF, video / x - ms - asf, asf)

#define KMIME_TYPE_AUDIO_MAP(XX)                                               \
  XX(KAUDIO_MP3, audio / mpeg, mp3)                                            \
  XX(KAUDIO_OGG, audio / ogg, ogg)                                             \
  XX(KAUDIO_M4A, audio / x - m4a, m4a)                                         \
  XX(KAUDIO_AAC, audio / aac, aac)                                             \
  XX(KAUDIO_PCMA, audio / PCMA, pcma)                                          \
  XX(KAUDIO_OPUS, audio / opus, opus)

#define KMIME_TYPE_FONT_MAP(XX)                                                \
  XX(KFONT_TTF, font / ttf, ttf)                                               \
  XX(KFONT_OTF, font / otf, otf)                                               \
  XX(KFONT_WOFF, font / woff, woff)                                            \
  XX(KFONT_WOFF2, font / woff2, woff2)

#define KHTTP_CONTENT_TYPE_MAP(XX)                                             \
  KMIME_TYPE_TEXT_MAP(XX)                                                      \
  KMIME_TYPE_APPLICATION_MAP(XX)                                               \
  KMIME_TYPE_MULTIPART_MAP(XX)                                                 \
  KMIME_TYPE_IMAGE_MAP(XX)                                                     \
  KMIME_TYPE_VIDEO_MAP(XX)                                                     \
  KMIME_TYPE_AUDIO_MAP(XX)                                                     \
  KMIME_TYPE_FONT_MAP(XX)

enum KHttpContentType {
#define XX(name, string, suffix) name,
  KHTTP_CONTENT_TYPE_NONE = 0,

  KHTTP_CONTENT_TYPE_TEXT = 100,
  KMIME_TYPE_TEXT_MAP(XX)

      KHTTP_CONTENT_TYPE_APPLICATION = 200,
  KMIME_TYPE_APPLICATION_MAP(XX)

      KHTTP_CONTENT_TYPE_MULTIPART = 300,
  KMIME_TYPE_MULTIPART_MAP(XX)

      KHTTP_CONTENT_TYPE_IMAGE = 400,
  KMIME_TYPE_IMAGE_MAP(XX)

      KHTTP_CONTENT_TYPE_VIDEO = 500,
  KMIME_TYPE_VIDEO_MAP(XX)

      KHTTP_CONTENT_TYPE_AUDIO = 600,
  KMIME_TYPE_AUDIO_MAP(XX)

      KHTTP_CONTENT_TYPE_FONT = 700,
  KMIME_TYPE_FONT_MAP(XX)

      KHTTP_CONTENT_TYPE_UNDEFINED = 1000
#undef XX
};

class KStringCaseLess : public std::less<std::string> {
public:
  bool operator()(const std::string &lhs, const std::string &rhs) const {
    return strcasecmp(lhs.c_str(), rhs.c_str()) < 0;
  }
};

using KHttpHeaders = std::map<std::string, std::string, KStringCaseLess>;
using KHttpParams = std::map<std::string, std::string, KStringCaseLess>;

KOALA_API KHttpHeaders kDefaultHeaders;
KOALA_API KHttpParams kDefaultParams;
} // namespace
