#pragma once

#include "common/Utils.h"
#include <map>
#include <string>
#include <vector>

namespace koala {
using KQueryResult = std::vector<std::map<std::string, std::string>>;

enum KDataBaseType { KDBT_MYSQL, KDBT_SQLITE, KDBT_ORACLE, KDBT_DAMENG };
enum KDataValueType { kDVT_BIGINT, KDVT_DATETIME, KDVT_STRING };

class IKDatabase {
public:
  using Ptr = std::shared_ptr<IKDatabase>;
  virtual ~IKDatabase() = default;
  virtual bool connect(const std::string &url, const std::string &database,
                       const std::string &user = "",
                       const std::string &password = "") = 0;
  virtual void disconnect() = 0;
  virtual KQueryResult query() = 0;
  virtual KQueryResult query(const std::string &sql) = 0;
  virtual bool execute(const std::string &sql) = 0;
  virtual bool begin() = 0;
  virtual bool commit() = 0;
  virtual bool rollback() = 0;
  virtual bool prepare(const std::string &sql) = 0;
  virtual IKDatabase &bind(uint32_t index, const std::string &value) = 0;
  virtual IKDatabase &bind(uint32_t index, const std::string &value,
                           int type) = 0;
  virtual IKDatabase &bind(uint32_t index, const char *value) = 0;
  virtual IKDatabase &bind(uint32_t index, int32_t value) = 0;
  virtual IKDatabase &bind(uint32_t index, uint32_t value) = 0;
  virtual IKDatabase &bind(uint32_t index, int64_t value) = 0;
  virtual IKDatabase &bind(uint32_t index, uint64_t value) = 0;
  virtual IKDatabase &bind(uint32_t index, double value) = 0;
  virtual IKDatabase &bind(uint32_t index, bool value) = 0;
  virtual IKDatabase &bind(uint32_t index, std::istream *blob) = 0;
  virtual IKDatabase &bind(uint32_t index) = 0;
  virtual int update() = 0;
  virtual KDBError getLastError() const = 0;
};

KEXTERN KOALA_API IKDatabase::Ptr
KCreateDatabase(KDataBaseType type = KDBT_MYSQL);

} // namespace koala