#pragma once

#include "common/Utils.h"
#include <map>
#include <set>
#include <vector>

namespace koala {
class IKRedis {
public:
  using Ptr = std::shared_ptr<IKRedis>;
  virtual ~IKRedis() = default;

  virtual bool connect(const std::string &host, uint16_t port,
                       const std::string &password = "") = 0;
  virtual void disconnect() = 0;
  virtual bool isConnected() const = 0;

  virtual bool set(const std::string &key, const std::string &value) = 0;
  virtual bool get(const std::string &key, std::string &value) = 0;
  virtual bool del(const std::string &key) = 0;
  virtual bool exists(const std::string &key) const = 0;

  virtual bool hset(const std::string &key, const std::string &field,
                    const std::string &value) = 0;
  virtual bool hget(const std::string &key, const std::string &field,
                    std::string &value) = 0;
  virtual bool hdel(const std::string &key, const std::string &field) = 0;
  virtual bool hgetall(const std::string &key,
                       std::map<std::string, std::string> &result) = 0;

  virtual bool lpush(const std::string &key, const std::string &value) = 0;
  virtual bool rpush(const std::string &key, const std::string &value) = 0;
  virtual bool lpop(const std::string &key, std::string &value) = 0;
  virtual bool rpop(const std::string &key, std::string &value) = 0;
  virtual bool lrange(const std::string &key, int64_t start, int64_t end,
                      std::vector<std::string> &result) = 0;

  virtual bool sadd(const std::string &key, const std::string &value) = 0;
  virtual bool srem(const std::string &key, const std::string &value) = 0;
  virtual bool smembers(const std::string &key,
                        std::set<std::string> &result) = 0;

  virtual bool zadd(const std::string &key, double score,
                    const std::string &member) = 0;
  virtual bool zrem(const std::string &key, const std::string &member) = 0;
  virtual bool zrange(const std::string &key, int64_t start, int64_t end,
                      std::vector<std::string> &result) = 0;

  virtual bool multi() = 0;
  virtual bool exec() = 0;
  virtual bool discard() = 0;

  virtual KDBError getLastError() const = 0;
};

KEXTERN KOALA_API IKRedis::Ptr KCreateRedis();
} // namespace koala
