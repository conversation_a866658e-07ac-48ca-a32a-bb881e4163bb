#pragma once
#include "common/Utils.h"
#include <memory>

namespace koala {
class IMemoryPool {
public:
  using Ptr = std::shared_ptr<IMemoryPool>;
  virtual ~IMemoryPool() = default;
  virtual std::shared_ptr<void> alloc() = 0;
  virtual std::size_t blockSize() const = 0;
  virtual int allocated() const = 0;
  virtual int available() const = 0;
};

KOALA_API IMemoryPool::Ptr KCreateMemoryPool(std::size_t blockSize);

} // namespace koala