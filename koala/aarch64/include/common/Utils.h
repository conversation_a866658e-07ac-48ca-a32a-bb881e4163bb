#pragma once

#include <memory>
#include <string>

#ifndef KEXTERN
#define KEXTERN extern "C"
#endif

#if defined(_WIN32)
#define KOALA_API __declspec(dllexport)
#elif defined(__GNUC__) && (__GNUC__ >= 4)
#define KOALA_API __attribute__((visibility("default")))
#else
#define KOALA_API
#endif

#define KSAFE_DELETE(ptr)                                                      \
  do {                                                                         \
    if (ptr != nullptr) {                                                      \
      delete ptr;                                                              \
      ptr = nullptr;                                                           \
    }                                                                          \
  } while (0)

#define KMEM_ALIGN(d, a) (((d) + (a - 1)) & ~(a - 1))
#define KMEM_ALIGIN_SIZE 64
#define KMAX_PATH 260
#define KDEFAULT_PACKAGE_MAX_LENGTH (1 << 21)

#define KSERVICE_EXPORT(SERVICE_CLASS)                                         \
  KEXTERN KOALA_API koala::IKService *createService(                           \
      koala::IKApplication *app) {                                             \
    return new SERVICE_CLASS(app);                                             \
  }

#define KDISABLE_COPY(Class)                                                   \
  Class(const Class &) = delete;                                               \
  Class &operator=(const Class &) = delete;

#define KDISABLE_MOVE(Class)                                                   \
  Class(Class &&) = delete;                                                    \
  Class &operator=(Class &&) = delete;

#define KDISABLE_COPY_MOVE(Class)                                              \
  KDISABLE_COPY(Class)                                                         \
  KDISABLE_MOVE(Class)

namespace koala {
template <class T> class Singleton {
public:
  static T &getInstance() {
    static T v;
    return v;
  }
};

template <class T, int N> class SingletonWithArgs {
public:
  static T &getInstance() {
    static T v(N);
    return v;
  }
};

template <class T> class SingletonPtr {
public:
  static std::shared_ptr<T> getInstance() {
    static std::shared_ptr<T> v(new T);
    return v;
  }
};

enum kLoadBlance {
  KLB_RoundRobin,
  KLB_Random,
  KLB_LeastConnections,
  KLB_IpHash,
  KLB_UrlHash,
};

enum KUnpackMode {
  KUNPACK_MODE_NONE,
  KUNPACK_BY_FIXED_LENGTH,
  KUNPACK_BY_DELIMITER,
  KUNPACK_BY_LENGTH_FIELD,
};

enum KUnpackCoding {
  KENCODE_BY_VARINT = 17,
  KENCODE_BY_LITTEL_ENDIAN = 1234,
  KENCODE_BY_BIG_ENDIAN = 4321,
};

struct KUnpackSetting {
  KUnpackMode mode = KUNPACK_MODE_NONE;
  uint32_t package_max_length;
  union {
    struct {
      uint32_t fixed_length;
    };
    struct {
      uint8_t delimiter[8];
      uint16_t delimiter_bytes;
    };
    struct {
      uint16_t start_offset;
      uint16_t start_field_bytes;
      uint16_t start_field[8];
      uint16_t end_field_bytes;
      uint16_t end_field[8];
      uint16_t body_offset;
      uint16_t length_field_offset;
      uint16_t length_field_bytes;
      short length_adjustment;
      KUnpackCoding length_field_coding;
    };
  };
};

struct KDBError {
  int code;
  std::string message;
};

KOALA_API char *KWorkDirectory(char *buf, int size);
KOALA_API const char *KApiVersion();
KOALA_API const char *KHttpVersoin();
KOALA_API std::string KCreateUUID();
KOALA_API std::string KCreateShortUUID();
} // namespace koala
