#pragma once

#include "common/Utils.h"

namespace koala {

#define KC<PERSON>OR_CLR "\033[0m"
#define KCOLOR_BLACK "\033[30m"
#define KC<PERSON>OR_RED "\033[31m"
#define <PERSON><PERSON><PERSON>_GREEN "\033[32m"
#define KCOLOR_YELLOW "\033[33m"
#define KC<PERSON>OR_BLUE "\033[34m"
#define KC<PERSON>OR_PURPLE "\033[35m"
#define KCOLOR_SKYBLUE "\033[36m"
#define KC<PERSON>OR_WHITE "\033[37m"

#define KC<PERSON>OR_BLK_WHT "\033[40;37m"
#define KCOLOR_RED_WHT "\033[41;37m"
#define KCOLOR_GREEN_WHT "\033[42;37m"
#define KCOLOR_YELLOW_WHT "\033[43;37m"
#define KCOLOR_BLUE_WHT "\033[44;37m"
#define KCOLOR_PURPLE_WHT "\033[45;37m"
#define KCOLOR_SKYBLUE_WHT "\033[46;37m"
#define KC<PERSON>OR_WHT_BLK "\033[47;30m"

#define KLOG_LEVEL_MAP(XXX)                                                    \
  XXX(KLOG_LEVEL_DEBUG, "DEBUG", KCOLOR_WHITE)                                 \
  XXX(KLOG_LEVEL_INFO, "INFO ", KCOLOR_GREEN)                                  \
  XXX(KLOG_LEVEL_WARN, "WARN ", KCOLOR_YELLOW)                                 \
  XXX(KLOG_LEVEL_ERROR, "ERROR", KCOLOR_RED)                                   \
  XXX(KLOG_LEVEL_FATAL, "FATAL", KCOLOR_RED_WHT)

enum KLogLevel {
  KLOG_LEVEL_VERBOSE = 0,
#define XXX(id, str, clr) id,
  KLOG_LEVEL_MAP(XXX)
#undef XXX
      KLOG_LEVEL_SILENT
};

typedef struct logger_s KLogger;
KOALA_API KLogger *KCreateLogger();
KOALA_API void KDestroyLogger(KLogger *logger);
KOALA_API void KLoggerSetLevel(KLogger *logger, int level);

KOALA_API void KLoggerSetFormat(KLogger *logger, const char *fmt);
KOALA_API void KLoggerSetMaxBufsize(KLogger *logger, uint32_t bufsize);
KOALA_API void KLoggerEnableColor(KLogger *logger, int on);
KOALA_API int KLoggerPrint(KLogger *logger, int level, const char *fmt, ...);

KOALA_API void KLoggerSetFile(KLogger *logger, const char *filepath);
KOALA_API void KLoggerSetMaxFilesize(KLogger *logger, uint64_t filesize);

KOALA_API void KLoggerSetRemainDays(KLogger *logger, int days);
KOALA_API void KLoggerEnableFsync(KLogger *logger, int on);
KOALA_API void KLoggerFsync(KLogger *logger);
KOALA_API const char *KLoggerGetLogFile(KLogger *logger);

KOALA_API KLogger *KLoggerDefault();
KOALA_API void KLoggerDestoryDefault(void);

#define klog koala::KLoggerDefault()
#define klog_destory() koala::KLoggerDestoryDefault()
#define klog_disable() koala::KLoggerSetLevel(klog, KLOG_LEVEL_SILENT)
#define klog_set_file(filepath) koala::KLoggerSetFile(klog, filepath)
#define klog_set_level(level) koala::KLoggerSetLevel(klog, level)
#define klog_set_format(format) koala::KLoggerSetFormat(klog, format)
#define klog_enable_color(on) koala::KLoggerEnableColor(klog, on)
#define klog_set_max_filesize(filesize)                                        \
  koala::KLoggerSetMaxFilesize(klog, filesize)
#define klog_set_remain_days(days) koala::KLoggerSetRemainDays(klog, days)
#define klog_enable_fsync() koala::KLoggerEnableFsync(klog, 1)
#define klog_disable_fsync() koala::KLoggerEnableFsync(klog, 0)
#define klog_fsync() koala::KLoggerFsync(klog)
#define klog_get_file() koala::KLoggerGetLogFile(klog)

KOALA_API void KConsolePrint(int level, const char *fmt, ...);

#define klog_debug(fmt, ...)                                                   \
  KLoggerPrint(klog, koala::KLOG_LEVEL_DEBUG, fmt, ##__VA_ARGS__)
#define klog_info(fmt, ...)                                                    \
  KLoggerPrint(klog, koala::KLOG_LEVEL_INFO, fmt, ##__VA_ARGS__)
#define klog_warn(fmt, ...)                                                    \
  KLoggerPrint(klog, koala::KLOG_LEVEL_WARN, fmt, ##__VA_ARGS__)
#define klog_error(fmt, ...)                                                   \
  KLoggerPrint(klog, koala::KLOG_LEVEL_ERROR, fmt, ##__VA_ARGS__)
#define klog_fatal(fmt, ...)                                                   \
  KLoggerPrint(klog, koala::KLOG_LEVEL_FATAL, fmt, ##__VA_ARGS__)

#define KCONSOLE_DEBUG(fmt, ...)                                               \
  KConsolePrint(koala::KLOG_LEVEL_DEBUG, fmt, ##__VA_ARGS__)
#define KCONSOLE_INFO(fmt, ...)                                                \
  KConsolePrint(koala::KLOG_LEVEL_INFO, fmt, ##__VA_ARGS__)
#define KCONSOLE_WARN(fmt, ...)                                                \
  KConsolePrint(koala::KLOG_LEVEL_WARN, fmt, ##__VA_ARGS__)
#define KCONSOLE_ERROR(fmt, ...)                                               \
  KConsolePrint(koala::KLOG_LEVEL_ERROR, fmt, ##__VA_ARGS__)
#define KCONSOLE_FATAL(fmt, ...)                                               \
  KConsolePrint(koala::KLOG_LEVEL_FATAL, fmt, ##__VA_ARGS__)
} // namespace koala