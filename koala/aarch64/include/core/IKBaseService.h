#pragma once

#include "IKQueueSink.h"
#include "message/KQueueMessage.h"

namespace koala {
class IKApplication;
class IKService {
public:
  virtual ~IKService() = default;
  virtual const char *name() const = 0;
  virtual bool initialize() = 0;
  virtual void uninitialize() = 0;
};

class KOALA_API IKBaseService : public IKService, public IKQueueSink {
public:
  explicit IKBaseService(IKApplication *app);
  virtual ~IKBaseService() = default;

  // IKService
  virtual const char *name() const = 0;
  virtual bool initialize() = 0;
  virtual void uninitialize() = 0;

  // IQueueSink
  virtual void onQueueMessage(KQueueMessage::Ptr msg) = 0;
  virtual IKEventLoop::Ptr getLoop();
  virtual uint32_t getId() const;
  virtual void subMessage(int id);

protected:
  IKApplication *_app;
  IKEventLoop::Ptr _loop;
  uint32_t _id;
};

} // namespace koala
