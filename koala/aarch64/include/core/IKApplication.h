#pragma once

#include "IKQueueService.h"
#include "common/IMemoryManager.h"
#include "event/IKEventLoop.h"
#include <cstddef>
#include <cstdint>

namespace koala {
class IKApplication {
public:
  virtual ~IKApplication() = default;
  virtual IKQueueService::Ptr getQueueService() = 0;
  virtual IKEventLoop::Ptr getNextLoop() = 0;
  virtual IMemoryManager::Ptr getMemoryManager() = 0;
  virtual uint32_t getServiceId() = 0;
};

} // namespace koala