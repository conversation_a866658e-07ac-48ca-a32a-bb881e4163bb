#pragma once

#include "common/Utils.h"
#include "message/KQueueMessage.h"

namespace koala {
class IKQueueSink;
class IKQueueService {
public:
  using Ptr = std::shared_ptr<IKQueueService>;
  virtual ~IKQueueService() = default;
  virtual void subMessage(int, IKQueueSink *) = 0;
  virtual void unsubMessage(int, IKQueueSink *) = 0;
  virtual void send(KQueueMessage::Ptr msg) = 0;
};

KEXTERN KOALA_API IKQueueService::Ptr KCreateQueueService();

} // namespace koala
