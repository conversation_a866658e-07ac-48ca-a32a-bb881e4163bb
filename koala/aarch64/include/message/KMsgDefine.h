#pragma once

namespace koala {
#define KMSG_TYPE_UN 0x00
#define KMSG_TYPE_TCP_CLIENT 0x1000
#define KMSG_TYPE_TCP_SERVER 0x2000
#define KMSG_TYPE_UDP_CLIENT 0x3000
#define KMSG_TYPE_UDP_SERVER 0x4000

#define KMSG_TYPE_HTTP_SERVER 0x5000
#define KMSG_TYPE_HTTP_CLIENT 0x6000
#define KMSG_TYPE_WS_SERVER 0x7000
#define KMSG_TYPE_WS_CLIENT 0x8000

#define KMSG_TYPE_MYSQL 0x9000
#define KMSG_TYPE_SQLITE 0xa000
#define KMSG_TYPE_REDIS 0xb000
#define KMSG_TYPE_ORACLE 0xc000
#define KMSG_TYPE_PLUGIN 0xd000

#define KMSG_TCP_OFFLINE 0x1001
#define KMSG_TCP_ONLINE 0x1002
#define KMSG_TCP_DATA 0x1003
#define KMSG_TCP_SHUTDOWN 0x1004

} // namespace koala