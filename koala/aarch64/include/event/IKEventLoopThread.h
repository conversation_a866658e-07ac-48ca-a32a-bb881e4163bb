#pragma once
#include "event/IKEventLoop.h"

namespace koala {

class IKEventLoopThread {
public:
  using Ptr = std::shared_ptr<IKEventLoopThread>;
  virtual ~IKEventLoopThread() = default;
  virtual const IKEventLoop::Ptr &loop() = 0;
  virtual bool isRunning() = 0;
  virtual void start(bool waitThreadStarted = true) = 0;
  virtual void stop(bool waitThreadStopped = false) = 0;
  virtual void join() = 0;
};

KEXTERN KOALA_API IKEventLoopThread::Ptr
KCreateEventLoopThread(IKEventLoop::Ptr loop = nullptr);

} // namespace koala