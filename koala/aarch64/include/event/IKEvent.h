#pragma once

#include "common/Utils.h"
#include <functional>
#include <memory>

namespace koala {
typedef uint64_t KTimerID;

#ifndef KINVALID_TIMER_ID
#define KINVALID_TIMER_ID ((koala::KTimerID)-1)
#endif

#ifndef KINFINITE
#define KINFINITE (uint32_t) - 1
#endif

class IKEvent {
public:
  using Ptr = std::shared_ptr<IKEvent>;
  virtual ~IKEvent() = default;
  virtual void *userData() const = 0;
};

typedef std::function<void(void *)> KEventCallback;
typedef std::function<void(KTimerID)> KTimerCallback;

KEXTERN KOALA_API IKEvent::Ptr KCreateEvent(KEventCallback cb,
                                            void *userData = nullptr);

} // namespace koala