#include "common/IMemoryManager.h"
#include <cstring>
#include <iostream>
#include <vector>

int main(int argc, char **argv) {
  auto mm = koala::KCreateMemoryManager();
  std::cout << "------------shareptr by auto--------------" << std::endl;
  {
    auto pool = mm->getMemoryPool(100);
    auto ptr3 = pool->alloc();
    memcpy(ptr3.get(), "hello", 6);
    std::cout << (char *)ptr3.get() << std::endl;
    memcpy(ptr3.get(), "world", 6);
    std::cout << (char *)ptr3.get() << std::endl;
    std::cout << "--------------alloc ptr3----------------" << std::endl;
    std::cout << "pool blocksize " << pool->blockSize() << " -- allocated "
              << pool->allocated() << " -- available " << pool->available()
              << std::endl;
  }
  {
    auto ptr4 = mm->alloc(200);
    auto pool = mm->getMemoryPool(200);
    std::cout << "--------------alloc ptr4----------------" << std::endl;
    std::cout << "pool blocksize " << pool->blockSize() << " -- allocated "
              << pool->allocated() << " -- available " << pool->available()
              << std::endl;
  }
  std::vector<std::shared_ptr<void>> blocks;
  for (auto i = 0; i < 102400; i++) {
    auto ptr = mm->alloc(500);
    blocks.push_back(ptr);
  }
  auto pool = mm->getMemoryPool(500);
  std::cout << "--------------alloc 10240----------------" << std::endl;
  std::cout << "pool blocksize " << pool->blockSize() << " -- allocated "
            << pool->allocated() << " -- available " << pool->available()
            << std::endl;
  blocks.clear();
  std::cout << "-------------release 10240---------------" << std::endl;
  std::cout << "pool blocksize " << pool->blockSize() << " -- allocated "
            << pool->allocated() << " -- available " << pool->available()
            << std::endl;
}
