#include "common/IMemoryPool.h"
#include "message/KQueueMessage.h"
#include <iostream>
#include <vector>

int main(int argc, char **argv) {
  auto pool = koala::KCreateMemoryPool(1024);
  std::vector<koala::KQueueMessage::Ptr> msgs;
  for (auto i = 0; i < 1024; i++) {
    auto buffer = pool->alloc();
    auto msg = koala::KCreateQueueMessage(buffer);
    msgs.push_back(msg);
  }

  for (auto msg : msgs) {
    std::cout << "src_id " << msg->_srcId << std::endl;
    std::cout << "dst_id " << msg->_dstId << std::endl;
    std::cout << "msg_id " << msg->_msgId << std::endl;
    std::cout << "msg_len " << msg->_msgLen << std::endl;
    std::cout << "msg_data " << msg->_msgData << std::endl;
    std::cout << "--------------" << std::endl;
    std::cout << sizeof(koala::KQueueMessage) << std::endl;
  }
  std::cout << "pool->allocated " << pool->allocated() << " pool->available "
            << pool->available() << std::endl;
  std::cout << "---------------------clear---------------------" << std::endl;
  msgs.clear();
  std::cout << "pool->allocated " << pool->allocated() << " pool->available "
            << pool->available() << std::endl;
}