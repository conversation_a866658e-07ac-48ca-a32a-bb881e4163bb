#include "event/IKEventLoopThread.h"
#include <iostream>
#include <thread>

static void onTimer(koala::KTimerID timerID, int n) {
  std::cout << "tid= " << std::this_thread::get_id() << " timerId= " << timerID
            << " time= " << time(NULL) << " n= " << n << std::endl;
}

#define USE_EXTERNAL_LOOP 0

int main(int argc, char **argv) {
#if USE_EXTERNAL_LOOP
  auto loop = koala::KCreateEventLoop();
  auto loop_thread = koala::KCreateEventLoopThread(loop);
#else
  auto loop_thread = koala::KCreateEventLoopThread();
  auto loop = loop_thread->loop();
#endif
  // runEvery 1s
  loop->setInterval(1000, std::bind(onTimer, std::placeholders::_1, 100));
  // runAfter 10s
  loop->setTimeout(10000, [&loop](koala::KTimerID timerID) { loop->stop(); });
  loop_thread->start();
  loop_thread->join();
}