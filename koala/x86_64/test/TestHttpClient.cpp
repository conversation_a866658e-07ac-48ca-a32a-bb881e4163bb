#include "net/http/IKHttpClient.h"
#include <iostream>
#include <thread>

int main(int argc, char **argv) {
  auto client = koala::KCreateHttpClient();
  client->setWithTLS("./cert/server.crt", "./cert/server.key");
  auto req = koala::KCreateHttpRequest();
  req->setMethod("GET");
  req->setUrl("https://www.baidu.com");
  req->setHeader("Connection", "close");
  req->setTimeout(10);
  std::cout << "================async request===============" << std::endl;
  client->sendAsync(req, [](const koala::IKHttpResponse::Ptr &resp) {
    std::cout << "test_http_async_client response thread tid="
              << std::this_thread::get_id() << std::endl;
    if (resp == NULL) {
      printf("request failed!\n");
    } else {
      printf("GET %d %s\r\n", resp->getStatusCode(), resp->getStatusMesasge());
    }
  });

  auto req1 = koala::KCreateHttpRequest();
  req1->setMethod("POST");
  req1->setUrl("https://www.baidu.com");
  req1->setHeader("Connection", "close");
  req1->setBody("{}");

  client->sendAsync(req1, [](const koala::IKHttpResponse::Ptr &resp) {
    if (resp == NULL) {
      printf("request failed!\n");
    } else {
      printf("POST %d %s\r\n", resp->getStatusCode(), resp->getStatusMesasge());
    }
  });

  std::this_thread::sleep_for(std::chrono::milliseconds(1000));
  std::cout << "================sync request================" << std::endl;
  auto resp = koala::KCreateHttpResponse();
  int ret = client->send(req, resp);
  if (ret != 0) {
    printf("request failed!\n");
  } else {
    printf("%d %s\r\n", resp->getStatusCode(), resp->getStatusMesasge());
    // printf("%s\n", resp->getBody().c_str());
  }

  resp = koala::KHttpsRequestGet("https://www.baidu.com", "./cert/server.crt",
                                 "./cert/server.key");
  std::cout << resp->getStatusCode() << " : " << resp->getStatusMesasge()
            << std::endl;

  return 0;
}