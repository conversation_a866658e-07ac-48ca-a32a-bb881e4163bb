#include "net/http/IKWebSocketClient.h"
#include <thread>

int main(int argc, char **argv) {
  auto loop = koala::KCreateEventLoop();
  auto wsc = koala::KCreateWebSocketClient(loop);
  wsc->connect("ws://192.168.1.125:8000/v1");
  loop->run();
  while (1) {
    wsc->send("hello ws server");
    std::this_thread::sleep_for(std::chrono::microseconds(1000));
  }
  wsc->close();
  loop->stop();
  return 0;
}