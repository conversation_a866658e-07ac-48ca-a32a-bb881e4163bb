#include "database/IKDataBase.h"
#include "event/IKEventLoop.h"
#include <iostream>
#include <thread>

class Test {
public:
  Test() {
    _db = koala::KCreateDatabase();
    if (!_db->connect("tcp://192.168.1.247:3306", "cloudconsume", "root",
                      "111111")) {
      auto err = _db->getLastError();
      std::cout << "connect to mysql error" << err.message << std::endl;
    }
  }
  ~Test() {}

  void write() {
    auto result = _db->execute("SHOW TABLES LIKE 'sss'");
    auto err = _db->getLastError();
    std::cout << "xxxx"
              << " " << err.code << " " << err.message << std::endl;
    std::string table = "user_8";
    std::string sql = "INSERT INTO " + table +
                      "(openid,unionid,phone,secret_key)"
                      " VALUES (?, ?, ?, ?)";
    std::string openid = "oPdhk6YYTJHrdo5sRaQfztIocBfE";
    std::string unionid = "";
    std::string phone = "13508410482";
    std::string key = "1234567890ABCDEF";
    _db->prepare(sql);
    _db->bind(1, openid).bind(2, unionid).bind(3, phone).bind(4, key);
    int row = _db->update();
    err = _db->getLastError();
    std::cout << "insert into result " << row << " " << err.code << " "
              << err.message << std::endl;
  }

private:
  koala::IKDatabase::Ptr _db;
};

int main(int argc, char **argv) {
  // auto loop = koala::KCreateEventLoop();
  // auto conn = koala::KCreateDatabase();
  // conn->connect("tcp://192.168.1.247:3306", "test", "root", "111111");

  // std::string sql = "INSERT INTO xx (id, name) VALUES (?, ?)";
  // conn->begin();
  // conn->prepare(sql);
  // conn->bind(1, 1).bind(2, std::string("马六"));
  // conn->update();
  // conn->bind(1, 2).bind(2, std::string("xxx"));
  // conn->update();
  // conn->bind(1, 3).bind(2, std::string("vvv"));
  // conn->update();
  // conn->commit();

  // sql = "UPDATE xx set name = ? where id = ?";
  // conn->prepare(sql);
  // conn->bind(1, std::string("王五")).bind(2, 3);
  // conn->update();

  // for (int i = 0; i < 10; i++) {
  //   loop->postEvent([loop, conn](void *) {
  //     std::cout << "线程ID " << std::this_thread::get_id() << std::endl;
  //     auto result = conn->query("select* from xx");
  //     for (auto &kv : result) {
  //       std::cout << "id = " << kv["id"] << " name = " << kv["name"]
  //                 << std::endl;
  //     }
  //   });
  // }
  Test t;
  t.write();
  // loop->run();
  return 0;
}