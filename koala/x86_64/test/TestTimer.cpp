#include "event/IKEventLoop.h"
#include <iostream>
#include <thread>

int main(int argc, char **argv) {
  auto loop = koala::KCreateEventLoop();
  std::cout << "main thread id " << std::this_thread::get_id() << std::endl;
  loop->setTimeout(1000, [](koala::KTimerID id) {
    std::cout << "timer start" << std::endl;
    std::cout << "loop tid= " << std::this_thread::get_id()
              << " timerId= " << id << " time= " << time(NULL) << std::endl;
    std::cout << "timer stop" << std::endl;
  });
  loop->run();
  return 0;
}