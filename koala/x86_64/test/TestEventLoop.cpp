#include "event/IKEventLoop.h"
#include <iostream>
#include <thread>

static void onTimer(koala::KTimerID timerID, int n) {
  std::cout << "tid= " << std::this_thread::get_id() << " timerId= " << timerID
            << " time= " << time(NULL) << " n= " << n << std::endl;
}

int main(int argc, char **argv) {
  auto loop = koala::KCreateEventLoop();
  loop->setInterval(1000, std::bind(onTimer, std::placeholders::_1, 100));
  loop->setTimeout(10000, [&loop](koala::KTimerID timerID) {
    loop->stop();
    std::cout << "exit after 10s" << std::endl;
  });

  loop->runThreadSafe([]() {
    std::cout << "run thread safe " << std::this_thread::get_id() << std::endl;
  });
  loop->run();
  return 0;
}