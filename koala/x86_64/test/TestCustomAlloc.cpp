#include "common/IMemoryPool.h"
#include <iostream>
#include <vector>

template <typename T> class KMemoryAlloctor : public std::allocator<T> {
public:
  KMemoryAlloctor(koala::IMemoryPool::Ptr &pool) : _pool(pool) {}
  T *allocate(size_t n) {
    _ptr = _pool->alloc();
    std::cout << "allocate ptr " << _ptr << " size " << n << std::endl;
    return (T *)_ptr.get();
  }

  void deallocate(T *ptr, size_t n) {
    std::cout << "deallocate ptr " << ptr << " size " << n << std::endl;
  }

private:
  koala::IMemoryPool::Ptr _pool;
  std::shared_ptr<void> _ptr;
};

int main() {
  auto pool = koala::KCreateMemoryPool(4096);
  KMemoryAlloctor<int> allocator(pool);
  std::vector<int, KMemoryAlloctor<int>> v(10, allocator);
  v[0] = 1;
  v[1] = 2;
  v.resize(100);
  std::cout << pool->allocated() << " " << pool->available() << std::endl;
  return 0;
}
