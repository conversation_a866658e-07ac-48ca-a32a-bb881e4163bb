#include "net/http/IKHttpServer.h"
#include <cstring>
#include <iostream>

int main(int argc, char **argv) {
  auto router = koala::KCreateHttpRouter();
  router->onHttpMiddleware(
      [](koala::IKHttpRequest *req, koala::IKHttpResponse *resp) {
        std::cout << "middle ware req " << req->getUrl() << std::endl;
        resp->setHeader("Server", "koala server");
        return KHTTP_STATUS_NEXT;
      });

  router->render("/", "./html");
  router->onHttpGet("/test", [](koala::IKHttpRequest *req,
                                koala::IKHttpResponse *resp) {
    std::cout << "path: " << req->getPath() << std::endl;
    std::cout << "full_path: " << req->getFullPath() << std::endl;
    std::cout << "Content-Type: "
              << req->getUrlEncoded("Content-Type", "default value")
              << std::endl;

    std::cout << "accept: " << req->getHeader("accept", "unknown") << std::endl;
    std::cout << "user-agent: " << req->getHeader("user-agent", "unknown")
              << std::endl;

    std::cout << "param id: " << req->getParam("id", "1000") << std::endl;
    std::cout << "param user: " << req->getParam("user", "admin") << std::endl;

    req->setParam("id", "2000");
    std::cout << "param id: " << req->getParam("id", "1000") << std::endl;
    std::string headers;
    req->dumpHeaders(headers);
    std::cout << headers << std::endl;
    std::string body;
    req->dumpBody(body);
    std::cout << body << std::endl;

    std::cout << req->getInt("id", 0) << std::endl;
    return resp->sendJson("{\"result\":\"ok\"}");
  });

  router->onHttpGet("/async", [](const koala::IKHttpRequest::Ptr &req,
                                 const koala::IKHttpWriter::Ptr &writer) {
    writer->begin();
    writer->writeHeader("X-Response-tid", "11111111111");
    writer->writeHeader("Content-Type", "text/plain");
    auto resp = koala::KCreateHttpResponse();
    resp->sendString("wwwwwwwwwwwwwwwxxxxxxxxxxxxxx");
    writer->writeResponse(resp);
    writer->end();
    std::cout << "async.........." << std::endl;
  });

  router->onHttpGet("/context", [](const koala::IKHttpContext::Ptr &ctx) {
    std::cout << "fullpath " << ctx->getFullpath() << std::endl;
    std::cout << "path " << ctx->getPath() << std::endl;
    std::cout << "xx " << ctx->getParam("xx") << std::endl;
    std::cout << "form data " << ctx->getFormData("xx") << std::endl;
    std::cout << "body " << ctx->getBody() << std::endl;
    return ctx->sendString(ctx->getUrl());
  });

  router->onHttpError(
      [](koala::IKHttpRequest *req, koala::IKHttpResponse *resp) {
        return resp->sendJson("{\"result\":\"ok\"}");
      });

  auto httpServer = koala::KCreateHttpServer();
  httpServer->setWithTLS("./cert/server.crt", "./cert/server.key");
  httpServer->setRouter(router);
  httpServer->setHost();
  httpServer->setPort(8080, 8443);
  return httpServer->run();
}