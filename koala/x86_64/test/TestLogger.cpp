#include "common/KLog.h"
#include "event/IKEventLoopThreadPool.h"
#include <iostream>
#include <sstream>
#include <thread>

int main(int argc, char **argv) {
  klog_info("xxxxx %s", "111");
  KCONSOLE_INFO("console info log %d", 222);
  klog_enable_color(1);
  klog_info("[color] xxxxx %s", "111");
  KCONSOLE_INFO("[color] console info log %d", 222);
  auto pool = koala::KCreateEventLoopThreadPool(8);
  pool->start(true);
  for (auto i = 0; i < 8; i++) {
    auto loop = pool->nextLoop();
    loop->setInterval(1, [i](koala::KTimerID id) {
      auto tid = std::this_thread::get_id();
      std::ostringstream ss;
      ss << tid;
      std::string dashes(i + 1, '-');
      klog_info("this thead log %s %s", ss.str().c_str(), dashes.c_str());
    });
  }
  pool->join();
  return 0;
}