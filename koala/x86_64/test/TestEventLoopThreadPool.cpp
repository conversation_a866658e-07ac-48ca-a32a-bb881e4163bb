#include "event/IKEventLoopThreadPool.h"
#include <iostream>
#include <thread>

static void onTimer(koala::KTimerID timerID, int n) {
  std::cout << "tid= " << std::this_thread::get_id() << " timerId= " << timerID
            << " time= " << time(NULL) << " n= " << n << std::endl;
}

int main(int argc, char **argv) {
  auto loops = koala::KCreateEventLoopThreadPool(4);
  loops->start(true);
  auto threadNum = loops->threadNum();
  for (int i = 0; i < threadNum; ++i) {
    koala::IKEventLoop::Ptr loop = loops->nextLoop();
    std::cout << "worker[" << i << "] tid= " << loop->tid() << std::endl;

    loop->runThreadSafe([loop]() {
      // runEvery 1s
      loop->setInterval(1000, std::bind(onTimer, std::placeholders::_1, 100));
    });

    loop->runThreadSafe([]() {
      std::cout << "run thread safe " << std::this_thread::get_id()
                << std::endl;
    });
  }

  // runAfter 10s
  loops->nextLoop()->setTimeout(
      10000, [&loops](koala::KTimerID timerID) { loops->stop(false); });
  loops->join();
}