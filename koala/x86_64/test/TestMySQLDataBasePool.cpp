#include "database/IKDataBasePool.h"
#include "event/IKEventLoop.h"
#include <iostream>
#include <thread>

int main(int argc, char **argv) {
  auto loop = koala::KCreateEventLoop();
  auto pool = koala::KCreateDataBasePool();
  pool->initialize("tcp://192.168.1.247:3306", "test", "root", "111111");
  auto conn = pool->getConn();
  std::string sql = "INSERT INTO xx (id, name) VALUES (?, ?)";
  conn->begin();
  conn->prepare(sql);
  conn->bind(1, 1).bind(2, std::string("马六"));
  conn->update();
  conn->bind(1, 2).bind(2, std::string("xxx"));
  conn->update();
  conn->bind(1, 3).bind(2, std::string("vvv"));
  conn->update();
  conn->commit();

  sql = "UPDATE xx set name = ? where id = ?";
  conn->prepare(sql);
  conn->bind(1, std::string("王五")).bind(2, 3);
  conn->update();

  pool->release(conn);

  for (int i = 0; i < 10; i++) {
    loop->postEvent([loop, pool](void *) {
      std::cout << "线程ID " << std::this_thread::get_id() << std::endl;
      auto conn = pool->getConn();
      auto result = conn->query("select* from xx");
      for (auto &kv : result) {
        std::cout << "id = " << kv["id"] << " name = " << kv["name"]
                  << std::endl;
      }
      pool->release(conn);
    });
  }
  loop->run();
  return 0;
}