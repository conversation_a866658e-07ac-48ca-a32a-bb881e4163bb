#include "net/tcp/IKTcpClient.h"
#include <iomanip>
#include <iostream>
#include <thread>

#pragma pack(push, 1)
struct TestPacket {
  unsigned int flag;
  int srcid;
  int dstid;
  int length;
  char *pdata;
};
#pragma pack(pop)

class TcpClientSink : public koala::ITcpClientSink {
public:
  virtual void onTcpClientConnected(koala::IKTcpClient::Ptr client,
                                    const char *peerAddr) {
    std::cout << "connect to " << peerAddr << std::endl;
  }

  virtual void onTcpClientMessage(koala::IKTcpClient::Ptr client,
                                  const char *peerAddr, const void *buf,
                                  int size) {
    std::cout << "recv from " << peerAddr << std::endl;
    std::cout << (const char *)buf << std::endl;
  }

  virtual void onTcpClientDisconnected(koala::IKTcpClient::Ptr client,
                                       const char *peerAddr) {
    std::cout << "disconnect to " << peerAddr << std::endl;
  }
};

int main(int argc, char **argv) {
  TcpClientSink sink;
  auto client = koala::KCreateTcpClient(&sink);
  client->setWithTLS("./cert/server.crt", "./cert/server.key");
  client->setReconnect();
  client->startConnect("127.0.0.1", 8554);
  TestPacket t;
  t.flag = 0xEFEF;
  t.srcid = 0x1;
  t.dstid = 0x2;
  t.length = 0;
  t.pdata = nullptr;
  for (auto i = 0; i < sizeof(TestPacket); i++) {
    const unsigned char *p = reinterpret_cast<const unsigned char *>(&t);
    std::cout << std::hex << std::setw(2) << std::setfill('0')
              << static_cast<int>(p[i]) << " ";
  }
  std::cout << std::endl;
  while (1) {
    client->send(&t, sizeof(TestPacket));
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
  }
  client->stop();
  return 0;
}