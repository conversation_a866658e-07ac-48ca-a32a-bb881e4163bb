#include <coroutine>
#include <iostream>
#include <thread>

class FileReader {
public:
  bool await_ready() {
    std::cout << "check status ready" << std::endl;
    return false;
  }

  void await_suspend(std::coroutine_handle<> handle) {
    std::this_thread::sleep_for(std::chrono::milliseconds(2000));
    std::cout << "sleep 2s" << std::endl;
    handle.resume();
  }

  int await_resume() {
    std::cout << "resume" << std::endl;
    return 100;
  }
};

class Task {
public:
  class promise_type {
  public:
    Task get_return_object() { return {}; }
    std::suspend_never initial_suspend() { return {}; }
    std::suspend_never final_suspend() noexcept { return {}; }
    void unhandled_exception() {}
    void return_void() {}
  };
};

Task print() {
  FileReader fr1;
  int total = co_await fr1;
  std::cout << "total1 " << total << std::endl;

  FileReader fr2;
  total += co_await fr2;
  std::cout << "total2 " << total << std::endl;
}

int main(int argc, char **argv) {
  print();
  return 0;
}