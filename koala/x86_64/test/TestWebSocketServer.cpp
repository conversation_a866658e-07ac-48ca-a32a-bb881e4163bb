#include "net/http/IKWebSocketServer.h"
#include <iostream>

class WebSocketSink : public koala::IKWebSocketServerSink {
public:
  WebSocketSink() {}
  virtual ~WebSocketSink() {}
  virtual void onWSServerOpen(const koala::IKWebSocketChannel::Ptr &channel,
                              const koala::IKHttpRequest::Ptr &req) {
    std::cout << "open" << req->getUrl() << std::endl;
  }

  virtual void onWSServerMessage(const koala::IKWebSocketChannel::Ptr &channel,
                                 const std::string &msg) {
    std::cout << "recv " << msg << std::endl;
    channel->send("hello client");
  }

  virtual void onWSServerClose(const koala::IKWebSocketChannel::Ptr &channel) {
    std::cout << "close" << std::endl;
  }
};

int main(int argc, char **argv) {
  auto ws = koala::KCreateWebSocketServer();
  auto router = koala::KCreateHttpRouter();
  router->onHttpGet("/ping", [](const koala::IKHttpContext::Ptr &ctx) {
    return ctx->send("pong");
  });
  WebSocketSink sink;
  ws->setWithTLS("server.crt", "server.key");
  ws->setSink(&sink);
  ws->setRouter(router);
  ws->setPingInterval(1000);
  ws->setPort(8000, 8443);
  ws->run();
  return 0;
}