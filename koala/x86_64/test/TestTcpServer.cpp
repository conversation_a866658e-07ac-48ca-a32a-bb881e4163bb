
#include "net/tcp/IKTcpServer.h"
#include <iomanip>
#include <iostream>

class TcpSink : public koala::IKTcpServerSink {
public:
  TcpSink() {}
  ~TcpSink() {}
  virtual void onTcpSessionConnected(koala::IKTcpServer::Ptr srv,
                                     uint32_t sessionId, const char *peerAddr) {
    std::cout << "connected " << peerAddr << " session id " << sessionId
              << std::endl;
    srv->send(sessionId, "welcome to tcp server", 21);
  }

  virtual void onTcpSessionMessage(koala::IKTcpServer::Ptr srv,
                                   uint32_t sessionId, const char *peerAddr,
                                   const void *data, size_t size) {
    char *cdata = (char *)data;
    // EF EF 0C 00 AA AA 68 65 6C 6C 6F 20 77 6F 72 6C 64 FF FF
    for (auto i = 0; i < size; ++i) {
      std::cout << "0x" << std::hex << std::setw(2) << std::setfill('0')
                << (cdata[i] & 0xff) << " ";
    }

    std::cout << std::endl;
    srv->send(sessionId, "hello client", 13);
  }

  virtual void onTcpSessionDisconnected(koala::IKTcpServer::Ptr srv,
                                        uint32_t sessionId,
                                        const char *peerAddr) {
    std::cout << "disconnected " << peerAddr << " session id " << sessionId
              << std::endl;
  }
};

int main(int argc, char **argv) {
  TcpSink sink;
  auto srv = koala::KCreateTcpServer(8080, &sink);
  // srv->setWithTLS("server.crt", "server.key");
  srv->setLoadBalance(koala::KLB_LeastConnections);
  srv->setMaxConnectionNum(4);
  // srv->setBinaryUnpack(0, 0, 2, 2, 2, 2);
  srv->start();
  while (1) {
  }
  srv->stop();
  return 0;
}