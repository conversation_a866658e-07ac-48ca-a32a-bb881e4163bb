#include "database/IKRedis.h"
#include <cassert>
#include <iostream>

void testConnect(koala::IKRedis::Ptr redis) {
  std::cout << "Testing Connect..." << std::endl;

  // Test connect with valid credentials
  bool connected = redis->connect("127.0.0.1", 6379);
  assert(connected && "Failed to connect to Red<PERSON>");

  // Test isConnected
  assert(redis->isConnected() && "Client should be connected");
  std::cout << "Connect test passed!" << std::endl;
}

void testSetAndGet(koala::IKRedis::Ptr redis) {
  std::cout << "Testing Set and Get..." << std::endl;

  // Test setting a value
  bool setResult = redis->set("key1", "value1");
  assert(setResult && "Failed to set key-value pair");

  // Test getting a value
  std::string value;
  bool getResult = redis->get("key1", value);
  assert(getResult && "Failed to get value for key1");
  assert(value == "value1" && "Retrieved value does not match expected value");

  std::cout << "Set and Get test passed!" << std::endl;
}

void testDeleteAndExists(koala::IKRedis::Ptr redis) {
  std::cout << "Testing Delete and Exists..." << std::endl;

  // Test deleting a key
  bool delResult = redis->del("key1");
  assert(delResult && "Failed to delete key1");

  // Test if the key exists
  bool existsResult = redis->exists("key1");
  assert(!existsResult && "Key should not exist after deletion");

  std::cout << "Delete and Exists test passed!" << std::endl;
}

void testHashOperations(koala::IKRedis::Ptr redis) {
  std::cout << "Testing Hash Operations..." << std::endl;

  // Test hset
  bool hsetResult = redis->hset("hashKey", "field1", "value1");
  assert(hsetResult && "Failed to set hash field");

  // Test hget
  std::string fieldValue;
  bool hgetResult = redis->hget("hashKey", "field1", fieldValue);
  assert(hgetResult && "Failed to get hash field");
  assert(fieldValue == "value1" && "Retrieved hash field value does not match");

  // Test hdel
  bool hdelResult = redis->hdel("hashKey", "field1");
  assert(hdelResult && "Failed to delete hash field");

  // Test hgetall
  std::map<std::string, std::string> hgetallResult;
  bool hgetallResultSuccess = redis->hgetall("hashKey", hgetallResult);
  assert(hgetallResultSuccess && "Failed to get all hash fields");
  assert(hgetallResult.empty() && "Hash should be empty after deletion");

  std::cout << "Hash Operations test passed!" << std::endl;
}

void testListOperations(koala::IKRedis::Ptr redis) {
  std::cout << "Testing List Operations..." << std::endl;

  // Test lpush and rpush
  bool lpushResult = redis->lpush("listKey", "value1");
  assert(lpushResult && "Failed to push value to list");

  bool rpushResult = redis->rpush("listKey", "value2");
  assert(rpushResult && "Failed to push value to list");

  // Test lpop and rpop
  std::string lpopValue, rpopValue;
  bool lpopResult = redis->lpop("listKey", lpopValue);
  assert(lpopResult && "Failed to pop value from list");
  assert(lpopValue == "value1" && "Popped value does not match");

  bool rpopResult = redis->rpop("listKey", rpopValue);
  assert(rpopResult && "Failed to pop value from list");
  assert(rpopValue == "value2" && "Popped value does not match");

  // Test lrange
  std::vector<std::string> lrangeResult;
  bool lrangeResultSuccess = redis->lrange("listKey", 0, -1, lrangeResult);
  assert(lrangeResultSuccess && "Failed to get range from list");
  assert(lrangeResult.empty() && "List should be empty after pop operations");

  std::cout << "List Operations test passed!" << std::endl;
}

void testSetOperations(koala::IKRedis::Ptr redis) {
  std::cout << "Testing Set Operations..." << std::endl;

  // Test sadd and srem
  bool saddResult = redis->sadd("setKey", "value1");
  assert(saddResult && "Failed to add value to set");

  bool sremResult = redis->srem("setKey", "value1");
  assert(sremResult && "Failed to remove value from set");

  // Test smembers
  std::set<std::string> smembersResult;
  bool smembersResultSuccess = redis->smembers("setKey", smembersResult);
  assert(smembersResultSuccess && "Failed to get set members");
  assert(smembersResult.empty() && "Set should be empty after removal");

  std::cout << "Set Operations test passed!" << std::endl;
}

void testZSetOperations(koala::IKRedis::Ptr redis) {
  std::cout << "Testing ZSet Operations..." << std::endl;

  // Test zadd
  bool zaddResult = redis->zadd("zsetKey", 1.0, "member1");
  assert(zaddResult && "Failed to add member to sorted set");

  // Test zrem
  bool zremResult = redis->zrem("zsetKey", "member1");
  assert(zremResult && "Failed to remove member from sorted set");

  // Test zrange
  std::vector<std::string> zrangeResult;
  bool zrangeResultSuccess = redis->zrange("zsetKey", 0, -1, zrangeResult);
  assert(zrangeResultSuccess && "Failed to get range from sorted set");
  assert(zrangeResult.empty() && "ZSet should be empty after removal");

  std::cout << "ZSet Operations test passed!" << std::endl;
}

void testTransaction(koala::IKRedis::Ptr redis) {
  std::cout << "Testing Multi/Exec/Discard..." << std::endl;

  // Test multi and exec
  bool multiResult = redis->multi();
  assert(multiResult && "Failed to start transaction");

  bool setResult = redis->set("key2", "value2");
  assert(setResult && "Failed to set key-value pair in transaction");

  bool execResult = redis->exec();
  assert(execResult && "Failed to execute transaction");

  // Test discard in a separate transaction
  multiResult = redis->multi();
  assert(multiResult && "Failed to start transaction for discard test");

  setResult = redis->set("key3", "value3");
  assert(setResult &&
         "Failed to set key-value pair in transaction for discard test");

  bool discardResult = redis->discard();
  assert(discardResult && "Failed to discard transaction");

  std::cout << "Transaction test passed!" << std::endl;
}

void testGetLastError(koala::IKRedis::Ptr redis) {
  std::cout << "Testing Get Last Error..." << std::endl;

  // Assuming KDBError enum or class, check if there is no error
  koala::KDBError error = redis->getLastError();
  assert(error.code == 0 && "There should be no error");

  std::cout << "Get Last Error test passed!" << std::endl;
}

void testDisconnect(koala::IKRedis::Ptr redis) {
  std::cout << "Testing Disconnect..." << std::endl;
  // Test disconnect
  redis->disconnect();
  assert(!redis->isConnected() && "Client should be disconnected");

  std::cout << "Disconnect test passed!" << std::endl;
}

int main(int argc, char **argv) {
  auto redis = koala::KCreateRedis();
  testConnect(redis);
  testSetAndGet(redis);
  testDeleteAndExists(redis);
  testHashOperations(redis);
  testListOperations(redis);
  testSetOperations(redis);
  testZSetOperations(redis);
  testTransaction(redis);
  testGetLastError(redis);
  testDisconnect(redis);
  std::cout << "All tests passed!" << std::endl;

  return 0;
}