#pragma once
#include "KMsgDefine.h"
#include "common/Utils.h"
#include <cstdint>
#include <cstring>
#include <memory>

namespace koala {
#define KNET_ADDR_LEN 32
#define KMSG_ID_ALL (0)
#define KINVALID_ID (-1)

#pragma pack(push, 1)
struct KQueueMessage {
  using Ptr = std::shared_ptr<KQueueMessage>;
  std::shared_ptr<void> _buf;
  uint16_t _srcId;
  uint16_t _dstId;
  uint16_t _msgId;
  uint16_t _msgType;
  uint32_t _msgLen;
  int8_t *_msgData;
};
#pragma pack(pop)

KOALA_API KQueueMessage::Ptr KCreateQueueMessage(std::shared_ptr<void> buffer);

struct IKTcpMessage {
  char _peerAddr[KNET_ADDR_LEN];
  int _session;
  int _msgType;
  char *_pData;
  int _dataLen;
};

KOALA_API KQueueMessage::Ptr KCreateTcpMessage(std::shared_ptr<void> buffer,
                                               int dataMsgType, uint16_t srcId,
                                               uint16_t msgId, int tcpType,
                                               uint32_t sessionId,
                                               const char *peerAddr = nullptr);

KOALA_API KQueueMessage::Ptr
KCreateTcpMessageWithData(std::shared_ptr<void> buffer, int dataMsgType,
                          uint16_t srcId, uint16_t msgId, int tcpType,
                          uint32_t sessionId, const void *data, int size,
                          const char *peerAddr = nullptr);

#define KCreateTcpServerOnlineMessage(ptr, src, id, session, peer)             \
  koala::KCreateTcpMessage(ptr, KMSG_TCP_ONLINE, src, id,                      \
                           KMSG_TYPE_TCP_SERVER, session, peer)

#define KCreateTcpServerOfflineMessage(ptr, src, id, session, peer)            \
  koala::KCreateTcpMessage(ptr, KMSG_TCP_OFFLINE, src, id,                     \
                           KMSG_TYPE_TCP_SERVER, session, peer)

#define KCreateTcpServerDataMessage(ptr, src, id, session, data, size)         \
  koala::KCreateTcpMessageWithData(ptr, KMSG_TCP_DATA, src, id,                \
                                   KMSG_TYPE_TCP_SERVER, session, data, size)

#define KCreateTcpServerShutdownMessage(ptr, src, id, session)                 \
  koala::KCreateTcpMessage(ptr, KMSG_TCP_SHUTDOWN, src, id,                    \
                           KMSG_TYPE_TCP_SERVER, session, nullptr)

#define KCreateTcpClientOnlineMessage(ptr, src, id, session, peer)             \
  koala::KCreateTcpMessage(ptr, KMSG_TCP_ONLINE, src, id,                      \
                           KMSG_TYPE_TCP_CLIENT, session, peer)

#define KCreateTcpClientOfflineMessage(ptr, src, id, session, peer)            \
  koala::KCreateTcpMessage(ptr, KMSG_TCP_OFFLINE, src, id,                     \
                           KMSG_TYPE_TCP_CLIENT, session, peer)

#define KCreateTcpClientDataMessage(ptr, src, id, data, size)                  \
  koala::KCreateTcpMessageWithData(ptr, KMSG_TCP_DATA, src, id,                \
                                   KMSG_TYPE_TCP_CLIENT, 0, data, size)
} // namespace koala