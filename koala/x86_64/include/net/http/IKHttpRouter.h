#pragma once

#include "common/Utils.h"
#include "net/http/IKHttpContext.h"
#include "net/http/IKHttpRequest.h"
#include "net/http/IKHttpResponse.h"
#include "net/http/IKHttpWriter.h"

#include <functional>
#include <variant>

namespace koala {
class IKHttpRouter {
public:
  using KHttpSyncHandler =
      std::function<int(IKHttpRequest *, IKHttpResponse *)>;

  using KHttpAsyncHandler = std::function<void(const IKHttpRequest::Ptr &,
                                               const IKHttpWriter::Ptr &)>;

  using KHttpContextHandler = std::function<int(const IKHttpContext::Ptr &)>;

  using KHttpcHandlerVariant =
      std::variant<KHttpSyncHandler, <PERSON>Http<PERSON>yncHand<PERSON>, KHttpContextHandler>;

  using Ptr = std::shared_ptr<IKHttpRouter>;
  virtual ~IKHttpRouter() = default;
  virtual void render(const char *path, const char *dir) = 0;
  virtual void enableForwardProxy() = 0;
  virtual int checkUrlAndMethod(const char *path, const char *method) = 0;
  virtual void allowCORS() = 0;
  virtual void onHttpHead(const char *path, KHttpcHandlerVariant vhandler) = 0;
  virtual void onHttpGet(const char *path, KHttpcHandlerVariant vhandler) = 0;
  virtual void onHttpPost(const char *path, KHttpcHandlerVariant vhandler) = 0;
  virtual void onHttpPut(const char *path, KHttpcHandlerVariant vhandler) = 0;
  virtual void onHttpDelete(const char *path,
                            KHttpcHandlerVariant vhandler) = 0;
  virtual void onHttpPatch(const char *path, KHttpcHandlerVariant vhandler) = 0;
  virtual void onHttpAny(const char *path, KHttpcHandlerVariant vhandler) = 0;
  virtual void onHttpMiddleware(KHttpcHandlerVariant vhandler) = 0;
  virtual void onHttpError(KHttpSyncHandler handler) = 0;
};

KEXTERN KOALA_API IKHttpRouter::Ptr KCreateHttpRouter();

} // namespace koala