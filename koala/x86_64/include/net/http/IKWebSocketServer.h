#pragma once

#include "IKHttpRouter.h"
#include "IKWebSocketChannel.h"
#include "common/Utils.h"
#include <functional>

namespace koala {
class IKWebSocketServerSink {
public:
  virtual ~IKWebSocketServerSink() = default;
  virtual void onWSServerOpen(const IKWebSocketChannel::Ptr &,
                              const IKHttpRequest::Ptr &) = 0;
  virtual void onWSServerMessage(const IKWebSocketChannel::Ptr &,
                                 const std::string &) = 0;
  virtual void onWSServerClose(const IKWebSocketChannel::Ptr &) = 0;
};

class IKWebSocketServer {
public:
  using Ptr = std::shared_ptr<IKWebSocketServer>;
  virtual ~IKWebSocketServer() = default;
  virtual int setWithTLS(const char *crtFile = nullptr,
                         const char *keyFile = nullptr,
                         const char *caFile = nullptr,
                         const char *caPath = nullptr,
                         int16_t verifyPeer = 0) = 0;
  virtual void setRouter(IKHttpRouter::Ptr router) = 0;
  virtual void setHost(const char *host = "0.0.0.0") = 0;
  virtual void setPort(int port = 0, int ssl_port = 0) = 0;
  virtual void setProcessNum(int num) = 0;
  virtual void setThreadNum(int num) = 0;
  virtual void setMaxWorkerConnectionNum(uint32_t num) = 0;
  virtual void setSink(IKWebSocketServerSink *sink) = 0;
  virtual void setPingInterval(int ms) = 0;
  virtual int run(const char *ip_port = nullptr, bool runLoop = true) = 0;
  virtual int stop() = 0;
};

KEXTERN KOALA_API IKWebSocketServer::Ptr KCreateWebSocketServer();

} // namespace koala