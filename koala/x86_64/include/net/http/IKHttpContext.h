#pragma once

#include "IKHttp.h"
#include "common/Utils.h"

namespace koala {

class IKHttpContext {
public:
  using Ptr = std::shared_ptr<IKHttpContext>;
  virtual ~IKHttpContext() = default;
  virtual KHttpMethod getMethod() = 0;
  virtual std::string getUrl() = 0;
  virtual std::string getPath() = 0;
  virtual std::string getFullpath() = 0;
  virtual std::string getHost() = 0;
  virtual std::string getHeader(const char *key,
                                const std::string &defValue = "") = 0;
  virtual std::string getParam(const char *key,
                               const std::string &defValue = "") = 0;
  virtual void setStatus(KHttpStatus status) = 0;
  virtual void setContentType(KHttpContentType type) = 0;
  virtual void setContentType(const char *type) = 0;
  virtual void setHeader(const char *key, const std::string &value) = 0;
  virtual std::string &getBody() = 0;
  virtual const std::string &getJson() = 0;
  virtual std::string getFormData(const char *name,
                                  const std::string &defValue = "") = 0;
  virtual void setBody(const std::string &body) = 0;
  virtual int send(const std::string &str,
                   KHttpContentType type = KAPPLICATION_JSON) = 0;
  virtual int sendString(const std::string &str) = 0;
  virtual int sendData(void *data, int len, bool nocopy = true) = 0;
  virtual int sendFile(const char *filepath) = 0;
  virtual int redirect(const std::string &location) = 0;
};
} // namespace koala