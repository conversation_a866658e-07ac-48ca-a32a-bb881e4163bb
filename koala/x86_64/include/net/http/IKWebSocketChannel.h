#pragma once

#include "common/Utils.h"

namespace koala {

enum KWebSocketCode {
  KWS_CODE_CONTINUE = 0x0,
  K<PERSON>_CODE_TEXT = 0x1,
  KWS_CODE_BINARY = 0x2,
  <PERSON><PERSON>_CODE_CLOSE = 0x8,
  <PERSON><PERSON>_CODE_PING = 0x9,
  K<PERSON>_CODE_PONG = 0xA,
};

class IKWebSocketChannel {
public:
  using Ptr = std::shared_ptr<IKWebSocketChannel>;
  virtual ~IKWebSocketChannel() = default;
  virtual int send(const std::string &msg, KWebSocketCode code = KWS_CODE_TEXT,
                   bool fin = true) = 0;
  virtual int send(const char *buf, int len,
                   KWebSocketCode code = KWS_CODE_BINARY, bool fin = true) = 0;
  virtual int send(const char *buf, int len, int fragment,
                   KWebSocketCode code = KWS_CODE_BINARY) = 0;
  virtual int sendPing() = 0;
  virtual int sendPong() = 0;
  virtual int close() = 0;
};
} // namespace koala