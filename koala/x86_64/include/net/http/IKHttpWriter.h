#pragma once

#include "IKHttp.h"
#include "IKHttpResponse.h"
#include "common/Utils.h"

namespace koala {

class IKHttpWriter {
public:
  using Ptr = std::shared_ptr<IKHttpWriter>;
  virtual ~IKHttpWriter() = default;
  virtual int begin() = 0;
  virtual int writeStatus(KHttpStatus status_codes) = 0;
  virtual int writeHeader(const char *key, const char *value) = 0;
  virtual int endHeaders(const char *key = nullptr,
                         const char *value = nullptr) = 0;
  virtual int writeChunked(const char *buf, int len = -1) = 0;
  virtual int endChunked() = 0;
  virtual int writeBody(const char *buf, int len = -1) = 0;
  virtual int writeResponse(IKHttpResponse::Ptr resp) = 0;
  virtual int end(const char *buf = nullptr, int len = -1) = 0;
};
} // namespace koala
