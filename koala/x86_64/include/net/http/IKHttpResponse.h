#pragma once

#include "IKHttp.h"
#include "common/Utils.h"

namespace koala {
class IKHttpResponse {
public:
  using Ptr = std::shared_ptr<IKHttpResponse>;
  virtual ~IKHttpResponse() = default;
  virtual KHttpStatus getStatusCode() = 0;
  virtual void setStatusCode(KHttpStatus status) = 0;
  virtual const char *getStatusMesasge() = 0;
  virtual void reset() = 0;
  virtual int redirect(const std::string &location) = 0;
  virtual bool isChunked() = 0;
  virtual bool isKeepalive() = 0;
  virtual bool isUpgrade() = 0;
  virtual void setHeader(const char *key, const std::string &value) = 0;
  virtual std::string getHeader(const char *key,
                                const std::string &defValue = "") = 0;
  virtual void setBody(const std::string &body) = 0;
  virtual const std::string &getBody() = 0;
  virtual const std::string &getJson() = 0;
  virtual std::string getFormData(const char *name,
                                  const std::string &defValue = "") = 0;
  virtual void setFormData(const char *name, const std::string &data) = 0;
  virtual void dumpHeaders(std::string &headers) = 0;
  virtual void dumpBody(std::string &body) = 0;
  virtual int sendString(const std::string &message) = 0;
  virtual int sendData(void *data, int len, bool nocopy = true) = 0;
  virtual int sendFile(const char *filepath) = 0;
  virtual int sendJson(const std::string &json) = 0;
};

KEXTERN KOALA_API IKHttpResponse::Ptr KCreateHttpResponse();

} // namespace koala