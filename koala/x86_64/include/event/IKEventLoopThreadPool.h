#pragma once
#include "event/IKEventLoop.h"

namespace koala {
class IKEventLoopThreadPool {
public:
  using Ptr = std::shared_ptr<IKEventLoopThreadPool>;
  virtual ~IKEventLoopThreadPool() = default;
  virtual int threadNum() const = 0;
  virtual void setThreadNum(int threadNum) = 0;
  virtual IKEventLoop::Ptr nextLoop(kLoadBlance lb = KLB_RoundRobin) = 0;
  virtual void start(bool waitThreadsStarted = false) = 0;
  virtual void stop(bool waitThreadsStopped = false) = 0;
  virtual void join() = 0;
};

KEXTERN KOALA_API IKEventLoopThreadPool::Ptr
KCreateEventLoopThreadPool(int threadNum);

} // namespace koala