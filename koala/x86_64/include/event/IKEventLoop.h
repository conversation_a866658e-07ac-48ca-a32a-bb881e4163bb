#pragma once
#include "event/IKEvent.h"

namespace koala {
typedef void *KBaseLoop;

using KTask = std::function<void()>;

class IKEventLoop {
public:
  using Ptr = std::shared_ptr<IKEventLoop>;
  virtual ~IKEventLoop() = default;
  virtual void run() = 0;
  virtual void stop() = 0;
  virtual void pause() = 0;
  virtual KTimerID setTimer(int ms, KTimerCallback cb,
                            uint32_t repeat = KINFINITE,
                            KTimerID timerID = KINVALID_TIMER_ID) = 0;
  virtual KTimerID setSafeTimer(int ms, KTimerCallback cb,
                                uint32_t repeat = KINFINITE,
                                KTimerID timerID = KINVALID_TIMER_ID) = 0;
  virtual KTimerID setTimeout(int ms, KTimerCallback cb) = 0;
  virtual KTimerID setInterval(int ms, KTimerCallback cb) = 0;
  virtual void killTimer(KTimerID timerID) = 0;
  virtual void resetTimer(KTimerID timerID, int ms = 0) = 0;
  virtual void runThreadSafe(KTask fn) = 0;
  virtual void postEvent(KEventCallback cb) = 0;
  virtual void postEvent(KEventCallback cb, void *userData) = 0;
  virtual long tid() = 0;
};

KEXTERN KOALA_API KBaseLoop KCreateBaseLoop();
KEXTERN KOALA_API IKEventLoop::Ptr KCreateEventLoop(KBaseLoop loop = nullptr);

} // namespace koala