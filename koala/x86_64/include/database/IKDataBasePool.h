#pragma once

#include "IKDataBase.h"

namespace koala {
class IKDataBasePool {
public:
  using Ptr = std::shared_ptr<IKDataBasePool>;
  virtual ~IKDataBasePool() = default;
  virtual bool initialize(const std::string &host, const std::string &database,
                          const std::string &user = "",
                          const std::string &password = "",
                          int poolSize = 4) = 0;
  virtual void ping() = 0;
  virtual IKDatabase::Ptr getConn() = 0;
  virtual void release(IKDatabase::Ptr conn) = 0;
  virtual void destroy() = 0;
};

KEXTERN KOALA_API IKDataBasePool::Ptr
KCreateDataBasePool(KDataBaseType type = KDBT_MYSQL);
} // namespace koala