# 系统配置
system:
  logfile: "logs/koala.log" # 日志文件路径
  # loglevel: [VERBOSE,DEBUG,INFO,WARN,ERROR,FATAL,SILENT]  # 可选的日志级别
  loglevel: DEBUG # 当前日志级别
  log_remain_days: 3 # 日志文件保留天数
  log_filesize: "16M" # 日志文件最大大小
  log_fsync: "true"
  worker_threads: 4 # 工作线程数
# 插件配置
plugins:
- id: 101
  name: "设备网关插件" # 插件名称
  path: "plugins/libCloudServerPlugin.so" # 插件路径
  service:
  - type: "tcp" # 目前支持三种协议 tcp udp database
    mode: "server" #tcp和udp支持client server两种模式，数据库支持 sqlite mysql oracle dameng（达梦）
    host: "0.0.0.0"
    port: 8554
    id: 0x1000 #插件和服务之间的通信id，不能重复
    thread_num: 4 # 1 accept + 3 worker
    keepalive: -1 #-1表示不启用，单位是ms
    ssl:
      #tls配置
      cert: ./cert/server.crt
      key: ./cert/server.key
    unpack:
      #固定长度解析模式
      #mode: "fixed" #fixed delimiter binary
      #length: 10

      #分隔符解析模式
      # mode: "delimiter"
      # delimiter:
      #   - 0x0a
      #   - 0x0d

      #二进制流解析，常用这种
      mode: binary
      package_max_length: 1024
      start_offset: 0 #起始位偏移位置
      start_field_bytes: 2 #起始位长度
      start_field:      #起始位数据
      - 0xef
      - 0xef
      end_field_bytes: 2 #结束位长度
      end_field:        #结束位数据
      - 0x55
      - 0x55

      length_offset: 4 #数据大小偏移
      length_bytes: 2 #数据大小长度
      body_offset: 20 #数据偏移，通常就是数据头长度
      adjustment: 0 #附加数据长度
      coding: "little" #little big, 大小端模式 一般情况默认是小端
  - type: "tcp"
    mode: "client"
    host: "*************"
    port: 8555
    id: 0x1001
  - type: "database"
    mode: "mysql"
    host: "127.0.0.1"
    port: 3306
    db_user: "root"
    db_pass: "root"
    db_name: "xxxx"
    id: 0x1002
- id: 102
  name: "消费服务区插件"
  path: "plugins/libGatewayPlugin.so"
  service: []
