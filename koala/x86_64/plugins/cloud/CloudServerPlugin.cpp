#include "CloudServerPlugin.h"
#include "core/IKApplication.h"
#include <iomanip>
#include <iostream>

CloudServerPlugin::CloudServerPlugin(koala::IKApplication *app)
    : koala::IKBaseService(app) {}

CloudServerPlugin::~CloudServerPlugin() {}

const char *CloudServerPlugin::name() const { return "CloudServer Plugin"; }

bool CloudServerPlugin::initialize() { return true; }

void CloudServerPlugin::uninitialize() {}

void CloudServerPlugin::onQueueMessage(koala::KQueueMessage::Ptr msg) {
  std::cout << "CloudServerPlugin::onQueueMessage" << std::endl;
  if (msg->_msgType == KMSG_TYPE_TCP_SERVER) {
    auto tcpMsg = reinterpret_cast<koala::IKTcpMessage *>(msg->_msgData);
    if (tcpMsg->_msgType == KMSG_TCP_ONLINE) {
      std::cout << "tcp online " << tcpMsg->_peerAddr << std::endl;
    } else if (tcpMsg->_msgType == KMSG_TCP_OFFLINE) {
      std::cout << "tcp offline " << tcpMsg->_peerAddr << std::endl;
    } else if (tcpMsg->_msgType == KMSG_TCP_DATA) {
      const unsigned char *p =
          reinterpret_cast<const unsigned char *>(tcpMsg->_pData);
      for (auto i = 0; i < tcpMsg->_dataLen; i++) {
        std::cout << std::hex << std::setw(2) << std::setfill('0')
                  << static_cast<int>(p[i]) << " ";
      }
      std::cout << std::endl;

      auto buffer1 = _app->getMemoryManager()->alloc(100);
      auto shutdown = KCreateTcpServerShutdownMessage(buffer1, _id, 0x1000,
                                                      tcpMsg->_session);
      _app->getQueueService()->send(shutdown);
    }

    auto buffer = _app->getMemoryManager()->alloc(100);
    auto respMsg = KCreateTcpServerDataMessage(
        buffer, _id, 0x1000, tcpMsg->_session, "hello cloud client", 18);
    _app->getQueueService()->send(respMsg);
  } else if (msg->_msgType == KMSG_TYPE_TCP_CLIENT) {
    auto tcpMsg = reinterpret_cast<koala::IKTcpMessage *>(msg->_msgData);
    if (tcpMsg->_msgType == KMSG_TCP_ONLINE) {
      std::cout << "tcp client connect to " << tcpMsg->_peerAddr << std::endl;
    } else if (tcpMsg->_msgType == KMSG_TCP_OFFLINE) {
      std::cout << "tcp client disconnect from " << tcpMsg->_peerAddr
                << std::endl;
    } else if (tcpMsg->_msgType == KMSG_TCP_DATA) {
      std::cout << "tcp client recv " << tcpMsg->_pData << std::endl;
      auto buffer = _app->getMemoryManager()->alloc(100);
      auto respMsg =
          KCreateTcpClientDataMessage(buffer, _id, 0x1001, "hello server", 12);
      _app->getQueueService()->send(respMsg);
    }
  }
}

KSERVICE_EXPORT(CloudServerPlugin)