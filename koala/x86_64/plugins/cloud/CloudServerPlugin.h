#pragma once

#include "core/IKBaseService.h"

class CloudServerPlugin : public koala::IKBaseService {
public:
  explicit CloudServerPlugin(koala::IKApplication *app);
  virtual ~CloudServerPlugin();

  // IKService
  virtual const char *name() const override;
  virtual bool initialize() override;
  virtual void uninitialize() override;

  // IQUeueSink
  virtual void onQueueMessage(koala::KQueueMessage::Ptr msg);
};