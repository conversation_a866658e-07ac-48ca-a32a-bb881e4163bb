#pragma once

#define OFFICICAL_ACCOUNT_TCP_2_HTTP_CHANNEL 8888

#define OFFICICAL_ACCOUNT_ADD_REQ (uint16_t)0x0101
// #define OFFICICAL_ACCOUNT_ADD_RESP (uint16_t)0x0102

#define OFFICICAL_ACCOUNT_DEL_REQ (uint16_t)0x0103
// #define OFFICICAL_ACCOUNT_DEL_RESP (uint16_t)0x0104

#define OFFICICAL_ACCOUNT_WARN_PUSH_REQ (uint16_t)0x0105
// #define OFFICICAL_ACCOUNT_WARN_PUSH_RESP (uint16_t)0x0106

#define OFFICICAL_ACCOUNT_SOCKET_STATUS_PUSH_REQ (uint16_t)0x0107
// #define OFFICICAL_ACCOUNT_SOCKET_STATUS_PUSH_RESP (uint16_t)0x0108

// 以毫秒为单位
#define MILLI_IN_MILLI 1
#define SECOND_IN_MILLI (1000 * MILLI_IN_MILLI)

// 以微秒为单位
#define MICRO_IN_MICRO 1
#define MILLI_IN_MICRO (1000 * MICRO_IN_MICRO)
#define SECOND_IN_MICRO (1000 * MILLI_IN_MICRO)

#define KB 1024
#define MB (1024 * KB)
#define GB (1024 * MB)

#define SAFE_DELETE(ptr)                                                       \
  if (ptr != nullptr) {                                                        \
    delete ptr;                                                                \
    ptr = nullptr;                                                             \
  }

#define SAFE_DELETE_ARR(pArr)                                                  \
  if (pArr != nullptr) {                                                       \
    delete[] pArr;                                                             \
    pArr = nullptr;                                                            \
  }
