#pragma once

#include <mutex>

template <typename T>
class CSington {
public:
    CSington() = delete;
    CSington(const CSington& s) = delete;
    ~CSington() = delete;

    static T* ins() {
        if (m_pInstance == nullptr) {
            std::lock_guard<std::mutex> lock(m_mutex);
            if (m_pInstance == nullptr) {
                T* ptmp = new T();
                m_pInstance = ptmp;
            }
        }

        return m_pInstance;
    }

    static void destroy() {
        if (m_pInstance != nullptr) {
            delete m_pInstance;
            m_pInstance = nullptr;
        }
    }

private:
    static T* m_pInstance;
    static std::mutex m_mutex;
};

template <typename T>
T* CSington<T>::m_pInstance = nullptr;

template <typename T>
std::mutex CSington<T>::m_mutex;
