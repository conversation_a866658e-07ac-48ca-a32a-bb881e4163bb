#pragma once

#include "CSington.h"
#include <algorithm>
#include <map>
#include <memory>
#include <string>

class ConsistentHash {
public:
  using Ptr = std::shared_ptr<ConsistentHash>;
  explicit ConsistentHash(int virtualNodes = 10)
      : _virtualNodeCount(virtualNodes) {}
  virtual ~ConsistentHash() {}

  void addNode(const std::string &node) {
    for (int i = 0; i < _virtualNodeCount; ++i) {
      std::string virtualNode = node + std::to_string(i);
      uint32_t hashValue = hash(virtualNode);
      _hashRing[hashValue] = virtualNode;
    }
  }
  void removeNode(const std::string &node) {
    for (int i = 0; i < _virtualNodeCount; ++i) {
      std::string virtualNode = node + std::to_string(i);
      uint32_t hashValue = hash(virtualNode);
      _hashRing.erase(hashValue);
    }
  }
  std::string getNode(const std::string &key) {
    if (_hashRing.empty())
      return "";

    uint32_t hashValue = hash(key);
    auto it = _hashRing.lower_bound(hashValue);
    if (it == _hashRing.end()) {
      return _hashRing.begin()->second;
    }
    return it->second;
  }

  std::map<uint32_t, std::string> nodes() const { return _hashRing; }

private:
  uint32_t hash(const std::string &key) {
    return std::hash<std::string>{}(key);
  }

private:
  std::map<uint32_t, std::string> _hashRing;
  int _virtualNodeCount;
};