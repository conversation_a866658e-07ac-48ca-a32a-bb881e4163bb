#pragma once
#include "net/http/IKHttpClient.h"
#include <mutex>
#include <queue>

#define HTTP_STATUS_INIT 0
#define HTTP_STATUS_DONE 1

struct HttpClientCtx {
  koala::IKHttpClient::Ptr _client;
  koala::IKHttpRequest::Ptr _req;
  koala::IKHttpResponse::Ptr _resp;
  int _status;
  using Ptr = std::shared_ptr<HttpClientCtx>;
};

class HttpClientPool {
public:
  using Ptr = std::shared_ptr<HttpClientPool>;
  explicit HttpClientPool(size_t size, const std::string &method,
                          const std::string &url,
                          KHttpHeaders &headers = kDefaultHeaders,
                          KHttpParams &params = kDefaultParams)
      : _method(method), _url(url), _params(params), _headers(headers) {
    for (size_t i = 0; i < size; ++i) {
      _pool.push(createHttpClient(method, url, headers, params));
    }
  }

  ~HttpClientPool() {}

  HttpClientCtx::Ptr acquire() {
    std::unique_lock<std::mutex> lock(_mutex);
    if (_pool.empty()) {
      auto pool = createHttpClient(_method, _url, _headers, _params);
      return pool;
    }
    auto instance = _pool.front();
    _pool.pop();
    return instance;
  }

  void release(HttpClientCtx::Ptr pool) {
    std::lock_guard<std::mutex> lock(_mutex);
    pool->_status = HTTP_STATUS_INIT;
    pool->_resp = nullptr;
    _pool.push(pool);
  }

private:
  std::queue<HttpClientCtx::Ptr> _pool;
  std::mutex _mutex;
  HttpClientCtx::Ptr createHttpClient(const std::string &method,
                                      const std::string &url,
                                      KHttpHeaders &headers = kDefaultHeaders,
                                      KHttpParams &params = kDefaultParams) {
    auto httpClient = std::make_shared<HttpClientCtx>();
    httpClient->_client = koala::KCreateHttpClient();
    httpClient->_client->setWithTLS("./cert/server.crt", "./cert/server.key");
    httpClient->_req = koala::KCreateHttpRequest();
    httpClient->_req->setMethod(method.c_str());
    httpClient->_req->setUrl(url.c_str());
    httpClient->_resp = nullptr;
    for (auto &header : headers) {
      httpClient->_req->setHeader(header.first.c_str(), header.second);
    }
    for (auto &param : params) {
      httpClient->_req->setParam(param.first.c_str(), param.second);
    }
    httpClient->_status = HTTP_STATUS_INIT;
    return httpClient;
  }

  std::string _method;
  std::string _url;
  KHttpParams _params;
  KHttpHeaders _headers;
};