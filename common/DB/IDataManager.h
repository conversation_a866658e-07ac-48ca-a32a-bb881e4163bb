#pragma once

#include "database/IKDataBasePool.h"
#include <list>
#include <memory>
#include <string>

#define FIND_FILTER_LIKE(field) #field " LIKE ':" #field "'"
#define FIND_FILTER_QUE(field) #field " = :" #field
#define FIND_FILTER_STR_QUE(field) #field " = ':" #field "'"
#define FIND_FILTER_GREATER(field) #field " > :" #field
#define FIND_FILTER_LESS(field, value) #field " < :" #value
#define FIND_FILTER_IN(field, value) #field " IN (" #value ")"

using CreateFunc = void (*)(std::string &sPrepare);

class IDBObject;
class IDatabase;
class IDataManager {
public:
  virtual void setDB(koala::IKDataBasePool::Ptr pDB) = 0;

  virtual koala::IKDataBasePool::Ptr DB() = 0;

  virtual koala::IKDataBasePool::Ptr database() const = 0;

  virtual void setTableName(const std::string &sTBName) = 0;

  virtual bool create() = 0;

  virtual bool add(IDBObject &obj) = 0;

  virtual bool del(IDBObject &obj, const std::string &sFilter = "Id = :Id") = 0;

  virtual bool replace(IDBObject &obj) = 0;

  virtual int count(IDBObject *pObj, const std::string &sFilter) const = 0;

  virtual std::list<IDBObject *> findObjs(const IDBObject *pObj,
                                          const std::string &sFilter,
                                          int nBegin = -1,
                                          int nCount = -1) const = 0;

  virtual std::list<IDBObject *> findObjsBySql(const IDBObject *pObj,
                                               std::string &sSql) const = 0;
};
