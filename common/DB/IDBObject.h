#pragma once

#include <string>

class CSqlQuery;
class IDBObject {
public:
  virtual ~IDBObject() {}

  virtual void insertPrepare(CSqlQuery &query, std::string &sPrepare) const = 0;

  virtual void updatePrepare(CSqlQuery &query, std::string &sPrepare) const = 0;

  virtual void deletePrepare(CSqlQuery &query, std::string &sPrepare) const = 0;

  virtual void findPrepare(CSqlQuery &query, std::string &sPrepare,
                           const std::string &sFilter) const = 0;

  virtual void bindValue(CSqlQuery &query) const = 0;
};
