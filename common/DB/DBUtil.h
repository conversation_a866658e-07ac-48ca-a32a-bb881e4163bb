#pragma once

#define ADD_FIELD_READONLY(Type, name) \
    public:\
        Type get##name() const\
        {\
            return m_##name;\
        }\
    private:\
        Type m_##name;

#define ADD_FIELD(Type, name) \
    public:\
        Type get##name() const\
        {\
            return m_##name;\
        }\
        void set##name(Type value)\
        {\
            m_##name = value;\
        }\
    private:\
        Type m_##name;

