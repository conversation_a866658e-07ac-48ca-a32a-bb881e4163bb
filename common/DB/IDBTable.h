#pragma once

#include "IDBObject.h"
#include "IDataManager.h"
#include <database/IKDataBasePool.h>
#include <list>
#include <map>
#include <memory>

class CSqlQuery;
using ParseFunc = IDBObject *(*)(const CSqlQuery &query);

class IDBTable {
public:
  virtual ~IDBTable() {}

  virtual void setDB(koala::IKDataBasePool::Ptr pDB) = 0;

  virtual void setName(const std::string &sName) = 0;

  virtual bool create(CreateFunc func) = 0;

  virtual bool save(const IDBObject &obj) = 0;

  virtual bool replace(const IDBObject &obj) = 0;

  virtual bool del(const IDBObject &obj,
                   const std::string &sFilter = "Id = :Id") = 0;

  virtual int count(IDBObject *pObj, const std::string &sFilter) const = 0;

  virtual std::list<IDBObject *> find(ParseFunc func, const IDBObject *pObj,
                                      const std::string &sFilter,
                                      int nBegin = -1, int nCount = -1) = 0;

  virtual std::list<IDBObject *>
  findObjsBySql(ParseFunc func, const IDBObject *pObj, std::string &sSql) = 0;
};
