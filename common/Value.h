#pragma once

#include <string>
#include <typeinfo>

template <class T>
class CValue
{
public:
    CValue(T &value) {}
    ~CValue() {}
};

template<>
class CValue<int>
{
public:
    CValue(int &value)
    {
        m_value = value;
    }

    ~CValue() {}

    std::string toString()
    {
        return std::to_string(m_value);
    }

private:
    int m_value;
};

template<>
class CValue<unsigned int>
{
public:
    CValue(unsigned int& value)
    {
        m_value = value;
    }

    ~CValue() {}

    std::string toString()
    {
        return std::to_string(m_value);
    }

private:
    unsigned int m_value;
};

template<>
class CValue<long>
{
public:
    CValue(long &value)
    {
        m_value = value;
    }

    ~CValue() {}

    std::string toString()
    {
        return std::to_string(m_value);
    }

private:
    long m_value;
};

template<>
class CValue<long long>
{
public:
    CValue(long long &value)
    {
        m_value = value;
    }

    ~CValue() {}

    std::string toString()
    {
        return std::to_string(m_value);
    }

private:
    long long m_value;
};

template<>
class CValue<unsigned long long>
{
public:
    CValue(unsigned long long& value)
    {
        m_value = value;
    }

    ~CValue() {}

    std::string toString()
    {
        return std::to_string(m_value);
    }

private:
    unsigned long long m_value;
};

template<>
class CValue<long unsigned int>
{
public:
    CValue(long unsigned int& value)
    {
        m_value = value;
    }

    ~CValue() {}

    std::string toString()
    {
        return std::to_string(m_value);
    }

private:
    long unsigned int m_value;
};

template<>
class CValue<short>
{
public:
    CValue(short &value)
    {
        m_value = value;
    }

    ~CValue() {}

    std::string toString()
    {
        return std::to_string(m_value);
    }

private:
    short m_value;
};

template<>
class CValue<unsigned short>
{
public:
    CValue(unsigned short& value)
    {
        m_value = value;
    }

    ~CValue() {}

    std::string toString()
    {
        return std::to_string(m_value);
    }

private:
    unsigned short m_value;
};

template<>
class CValue<char>
{
public:
    CValue(char &value)
    {
        m_value = value;
    }

    ~CValue() {}

    std::string toString()
    {
        return std::to_string(m_value);
    }

private:
    char m_value;
};

template<>
class CValue<unsigned char>
{
public:
    CValue(unsigned char& value)
    {
        m_value = value;
    }

    ~CValue() {}

    std::string toString()
    {
        return std::to_string(m_value);
    }

private:
    unsigned char m_value;
};

template<>
class CValue<float>
{
public:
    CValue(float &value)
    {
        m_value = value;
    }

    ~CValue() {}

    std::string toString()
    {
        return std::to_string(m_value);
    }

private:
    float m_value;
};

template<>
class CValue<double>
{
public:
    CValue(double &value)
    {
        m_value = value;
    }

    ~CValue() {}

    std::string toString()
    {
        return std::to_string(m_value);
    }

private:
    double m_value;
};

template<>
class CValue<std::string>
{
public:
    CValue(std::string &value)
    {
        m_value = value;
    }

    ~CValue() {}

    std::string toString()
    {
        return m_value;
    }

private:
    std::string m_value;
};
