#pragma once
#include <stddef.h>
#include <stdint.h>

inline uint8_t charToHex(uint8_t value) {
  if (value >= 0x30 && value <= 0x39) {
    return value - 0x30;
  } else if (value >= 0x41 && value <= 0x46) {
    return value - 0x37;
  } else if (value >= 0x61 && value <= 0x66) {
    return value - 32 - 0x37;
  }

  return 0x0;
}

inline uint8_t toHexChar(uint8_t value) {
  if (value >= 0 && value <= 9) {
    return value + 0x30;
  } else if (value >= 0x0A && value <= 0x0F) {
    return value + 0x37;
  }
  return 0x0;
}

inline void toHexString(const void *data, size_t dataSize, uint8_t *output) {
  const uint8_t *pv = reinterpret_cast<const uint8_t *>(data);
  for (size_t i = 0, j = 0; i < dataSize; i++, j += 2) {
    uint8_t high = ((pv[i] >> 4) & 0x0F);
    output[j] = toHexChar(high);
    uint8_t low = pv[i] & 0x0F;
    output[j + 1] = toHexChar(low);
  }
}