#pragma once

#include "SHead.h"
#include "STail.h"

#pragma pack(push, 1)
struct SUUIDGarageInfoResp {
  const static uint16_t MSG_ID = MAKE_MSGID(0x02, 0x27);

  SHead m_head;
  uint16_t m_nErrCode;    // 错误码
  uint32_t m_nGarageId;   // 车库编号
  uint8_t m_nGarageSvrId; // 车库监控服务编号
  uint8_t m_nBoardId;     // 主板编号
  uint8_t m_nSubId;       // 子设备编号
  uint16_t m_nTypeId;     // 设备类型
  uint8_t
      m_nOpenStatus; // 0-关闭 1-开启
                     // 插座通过此判断是否在使用来提示用户更换设备，门禁无需提示。按位取值，通常只需要1位或者2位有效
  STail m_tail;

  NS_ZERO_PROTOCOL(SUUIDGarageInfoResp, MSG_ID);
};

#pragma pack(pop)

#ifdef PROTOCOL_STREAM
inline nsStreamWriter &operator<<(nsStreamWriter &stream,
                                  const SUUIDGarageInfoResp &data) {
  stream << data.m_head << data.m_nErrCode << data.m_nGarageId
         << data.m_nGarageSvrId << data.m_nBoardId << data.m_nSubId
         << data.m_nTypeId << data.m_nOpenStatus << data.m_tail;

  NS_ASSERT(data.m_head.m_nMsgId == data.MSG_ID);
  return stream;
}

inline nsStreamReader &operator>>(nsStreamReader &stream,
                                  SUUIDGarageInfoResp &data) {
  stream >> data.m_head >> data.m_nErrCode >> data.m_nGarageId >>
      data.m_nGarageSvrId >> data.m_nBoardId >> data.m_nSubId >>
      data.m_nTypeId >> data.m_nOpenStatus >> data.m_tail;

  NS_ASSERT(data.m_head.m_nMsgId == data.MSG_ID);
  return stream;
}
#endif