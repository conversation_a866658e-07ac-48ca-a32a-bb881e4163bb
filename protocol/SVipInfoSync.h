#pragma once

#include "Protocol.h"
#include "SHead.h"
#include "STail.h"

// 会员同步请求
// 车库消费服务->云消费服务
#pragma pack(push, 1)

struct SVipInfoSync {
  const static uint16_t MSG_ID = MAKE_MSGID(0x04, 0x0E);

  SHead m_head;
  uint32_t m_CarPortId; // 车库id
  STail m_tail;

  NS_ZERO_PROTOCOL(SVipInfoSync, MSG_ID);
};

#pragma pack(pop)
#ifdef PROTOCOL_STREAM

inline nsStreamWriter &operator<<(nsStreamWriter &stream,
                                  const SVipInfoSync &data) {
  stream << data.m_head << data.m_CarPortId;
  stream << data.m_tail;
  return stream;
}

inline nsStreamReader &operator>>(nsStreamReader &stream, SVipInfoSync &data) {

  stream >> data.m_head >> data.m_CarPortId;
  stream >> data.m_tail;
  return stream;
}
#endif
