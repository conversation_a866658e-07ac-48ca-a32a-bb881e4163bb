#pragma once

#include "Protocol.h"
#include "SHead.h"
#include "STail.h"

#pragma pack(push, 1)

struct SSocketOffResq {
  const static uint16_t MSG_ID = MAKE_MSGID(0x02, 0x07);

  SHead m_head;

  uint8_t m_ServiceId;   // 车库监控服务编号
  uint8_t m_MainBroadId; // 主板编号
  uint8_t m_SubDevId;    // 子设备编号
  uint8_t m_SubIntId;    // 子接口编号
  uint16_t m_nRes;       // 错误码

  STail m_tail;

  NS_ZERO_PROTOCOL(SSocketOffResq, MSG_ID);
};

#pragma pack(pop)
#ifdef PROTOCOL_STREAM

inline nsStreamWriter &operator<<(nsStreamWriter &stream,
                                  const SSocketOffResq &data) {
  stream << data.m_head << data.m_ServiceId << data.m_MainBroadId
         << data.m_SubDevId << data.m_SubIntId << data.m_nRes << data.m_tail;
  return stream;
}

inline nsStreamReader &operator>>(nsStreamReader &stream,
                                  SSocketOffResq &data) {
  stream >> data.m_head >> data.m_ServiceId >> data.m_MainBroadId >>
      data.m_SubDevId >> data.m_SubIntId >> data.m_nRes >> data.m_tail;
  return stream;
}
#endif
