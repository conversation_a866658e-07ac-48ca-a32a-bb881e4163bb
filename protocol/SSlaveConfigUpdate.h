#pragma once

#include "SHead.h"
#include "STail.h"
#include "vector"

#pragma pack(push, 1)
struct SSlaveMainBorad {
  uint16_t mainBoardType;
  uint8_t mainBoardNum;
  uint8_t SubDevCnt;
};

struct SSlaveConfig {
  const static uint16_t MSG_ID = MAKE_MSGID(0x02, 0x01);

  SHead m_head;
  uint8_t m_MonitorServerNum;
  uint16_t m_MainBoardMgrCnt;
#ifdef PROTOCOL_STREAM
  std::vector<SSlaveMainBorad> m_pInfo;
#else
  SSlaveMainBorad* m_pInfo;
#endif
  STail m_tail;

  SSlaveConfig() { this->m_head.m_nMsgId = 0x0201; }
};

struct CameraInfo {
  uint8_t m_Account[12];
  uint8_t m_PassWord[12];
  uint8_t ip[4];
  uint32_t m_Channel1;
  uint32_t m_Channel2;
};

struct CameraServerInfo {
  uint8_t CameraServerNum;
  uint8_t CameraCnt;
  std::vector<CameraInfo> Camerainfo;
};

struct SSlaveCameraInfo {
  const static uint16_t MSG_ID = MAKE_MSGID(0x02, 0x02);
  SHead m_head;
  uint8_t m_MonitorServerNum;
  uint8_t m_UUID[12];
  uint16_t m_MainBoardMgrCnt;
  std::vector<CameraServerInfo> CameraServerinfo;
  STail m_tail;
  SSlaveCameraInfo() { this->m_head.m_nMsgId = 0x0202; }
};

#pragma pack(pop)

#ifdef PROTOCOL_STREAM
inline nsStreamWriter& operator<<(nsStreamWriter& stream,
                                  const SSlaveMainBorad& data) {
  stream << data.mainBoardType << data.mainBoardNum << data.SubDevCnt;
  return stream;
}

inline nsStreamReader& operator>>(nsStreamReader& stream,
                                  SSlaveMainBorad& data) {
  stream >> data.mainBoardType >> data.mainBoardNum >> data.SubDevCnt;
  return stream;
}

inline nsStreamWriter& operator<<(nsStreamWriter& stream,
                                  const SSlaveConfig& data) {
  stream << data.m_head << data.m_MonitorServerNum << data.m_MainBoardMgrCnt;
  for (size_t i = 0; i < data.m_pInfo.size(); i++) {
    stream << data.m_pInfo[i];
  }
  stream << data.m_tail;
  return stream;
}

inline nsStreamReader& operator>>(nsStreamReader& stream, SSlaveConfig& data) {
  stream >> data.m_head >> data.m_MonitorServerNum >> data.m_MainBoardMgrCnt;
  data.m_pInfo.resize(data.m_MainBoardMgrCnt);
  for (size_t i = 0; i < data.m_pInfo.size(); i++) {
    stream >> data.m_pInfo[i];
  }
  stream >> data.m_tail;
  return stream;
}

inline nsStreamWriter& operator<<(nsStreamWriter& stream,
                                  const CameraInfo& data) {
  for (size_t i = 0; i < 12; i++) {
    stream << data.m_Account[i];
  }
  for (size_t i = 0; i < 12; i++) {
    stream << data.m_PassWord[i];
  }
  for (size_t i = 0; i < 4; i++) {
    stream << data.ip[i];
  }
  stream << data.m_Channel1 << data.m_Channel2;

  return stream;
}

inline nsStreamReader& operator>>(nsStreamReader& stream, CameraInfo& data) {
  for (size_t i = 0; i < 12; i++) {
    stream >> data.m_Account[i];
  }
  for (size_t i = 0; i < 12; i++) {
    stream >> data.m_PassWord[i];
  }
  for (size_t i = 0; i < 4; i++) {
    stream >> data.ip[i];
  }
  stream >> data.m_Channel1 >> data.m_Channel2;
  return stream;
}

inline nsStreamWriter& operator<<(nsStreamWriter& stream,
                                  const CameraServerInfo& data) {
  stream << data.CameraServerNum << data.CameraCnt;
  for (size_t i = 0; i < data.Camerainfo.size(); i++) {
    stream << data.Camerainfo[i];
  }
  return stream;
}

inline nsStreamReader& operator>>(nsStreamReader& stream,
                                  CameraServerInfo& data) {
  stream >> data.CameraServerNum >> data.CameraCnt;
  data.Camerainfo.resize(data.CameraCnt);
  for (size_t i = 0; i < data.Camerainfo.size(); i++) {
    stream >> data.Camerainfo[i];
  }
  return stream;
}

inline nsStreamWriter& operator<<(nsStreamWriter& stream,
                                  const SSlaveCameraInfo& data) {
  stream << data.m_head << data.m_MonitorServerNum;
  for (size_t i = 0; i < 12; i++) {
    stream << data.m_UUID[i];
  }
  for (size_t i = 0; i < data.CameraServerinfo.size(); i++) {
    stream << data.CameraServerinfo[i];
  }
  stream << data.m_tail;
  return stream;
}

inline nsStreamReader& operator>>(nsStreamReader& stream,
                                  SSlaveCameraInfo& data) {
  stream >> data.m_head >> data.m_MonitorServerNum;
  for (size_t i = 0; i < 12; i++) {
    stream >> data.m_UUID[i];
  }
  data.CameraServerinfo.resize(data.m_MainBoardMgrCnt);
  for (size_t i = 0; i < data.CameraServerinfo.size(); i++) {
    stream >> data.CameraServerinfo[i];
  }
  stream >> data.m_tail;
  return stream;
}
#endif