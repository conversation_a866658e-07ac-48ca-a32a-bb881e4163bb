#pragma once

#include "Package.h"
#include "Protocol.h"
#include "SHead.h"
#include "STail.h"

#pragma pack(push, 1)
struct PackageCreateReq {
  const static uint16_t MSG_ID = MAKE_MSGID(0x03, 0x07);
  SHead m_head;
  char m_token[16]; // Token: Authentication token for user/session
  Package m_package;
  STail m_tail;
  NS_ZERO_PROTOCOL(PackageCreateReq, MSG_ID);
};

struct PackageCreateResp {
  const static uint16_t MSG_ID = MAKE_MSGID(0x03, 0x08);
  SHead m_head;
  int m_errcode;
  char m_errmsg[32];
  char m_packageId[16];
  STail m_tail;
  NS_ZERO_PROTOCOL(PackageCreateResp, MSG_ID);
};

#pragma pack(pop)