﻿#pragma once

#include "Protocol.h"
#include "SHead.h"
#include "STail.h"

#pragma pack(push, 1)

struct UserLoginReq {
  const static uint16_t MSG_ID = MAKE_MSGID(0x03, 0x01);
  SHead m_head;
  signed char m_user[16];
  signed char m_password[16];
  STail m_tail;
  NS_ZERO_PROTOCOL(UserLoginReq, MSG_ID);
};

struct UserLoginResp {
  const static uint16_t MSG_ID = MAKE_MSGID(0x03, 0x02);
  SHead m_head;
  int m_errcode;
  signed char m_errmsg[32];
  signed char m_token[16];
  STail m_tail;
  NS_ZERO_PROTOCOL(UserLoginResp, MSG_ID);
};

#pragma pack(pop)


#ifdef PROTOCOL_STREAM
inline nsStreamWriter &operator<<(nsStreamWriter &stream, const UserLoginReq &data) {
  stream << data.m_head;

  for (size_t i = 0; i < sizeof(data.m_user); ++i) {
    stream << data.m_user[i];
  }

  for (size_t i = 0; i < sizeof(data.m_password); ++i) {
    stream << data.m_password[i];
  }

  stream << data.m_tail;

  return stream;
}

inline nsStreamReader &operator>>(nsStreamReader &stream, UserLoginReq &data) {
    stream >> data.m_head;

    for (size_t i = 0; i < sizeof(data.m_user); ++i) {
      stream >> data.m_user[i];
    }

    for (size_t i = 0; i < sizeof(data.m_password); ++i) {
      stream >> data.m_password[i];
    }

    stream >> data.m_tail;

  return stream;
}

inline nsStreamWriter &operator<<(nsStreamWriter &stream, const UserLoginResp &data) {
    stream << data.m_head;
    stream << data.m_errcode;
    for (size_t i = 0; i < sizeof(data.m_errmsg); ++i) {
      stream << data.m_errmsg[i];
    }
    for (size_t i = 0; i < sizeof(data.m_token); ++i) {
      stream << data.m_token[i];
    }
    stream << data.m_tail;

  return stream;
}

inline nsStreamReader &operator>>(nsStreamReader &stream, UserLoginResp &data) {
    stream >> data.m_head;
    stream >> data.m_errcode;
    for (size_t i = 0; i < sizeof(data.m_errmsg); ++i) {
      stream >> data.m_errmsg[i];
    }
    for (size_t i = 0; i < sizeof(data.m_token); ++i) {
      stream >> data.m_token[i];
    }
    stream >> data.m_tail;

  return stream;
}
#endif
