#pragma once

#include "Package.h"
#include "Protocol.h"
#include "SHead.h"
#include "STail.h"

#pragma pack(push, 1)

struct PackageQueryReq {
  const static uint16_t MSG_ID = MAKE_MSGID(0x03, 0x0d);
  SHead m_head;
  char m_token[16]; // Token: Authentication token for user/session
  int m_index;
  int m_limit;
  STail m_tail;
  NS_ZERO_PROTOCOL(PackageQueryReq, MSG_ID);
};

struct PackageQueryResp {
  const static uint16_t MSG_ID = MAKE_MSGID(0x03, 0x0e);
  SHead m_head;
  int m_errcode;
  char m_errmsg[32];
  uint32_t m_lastId;
  int m_packageSize;
  Package m_package[10];
  STail m_tail;
  NS_ZERO_PROTOCOL(PackageQueryResp, MSG_ID);
};

#pragma pack(pop)