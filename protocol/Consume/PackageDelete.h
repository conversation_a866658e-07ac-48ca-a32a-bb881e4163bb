#pragma once

#include "Protocol.h"
#include "SHead.h"
#include "STail.h"

#pragma pack(push, 1)

struct PackageDeleteReq {
  const static uint16_t MSG_ID = MAKE_MSGID(0x03, 0x09);
  SHead m_head;
  char m_token[16];
  char m_packageId[16];
  STail m_tail;
  NS_ZERO_PROTOCOL(PackageDeleteReq, MSG_ID);
};

struct PackageDeleteResp {
  const static uint16_t MSG_ID = MAKE_MSGID(0x03, 0x0a);
  SHead m_head;
  int m_errcode;
  char m_errmsg[32];
  STail m_tail;
  NS_ZERO_PROTOCOL(PackageDeleteResp, MSG_ID);
};

#pragma pack(pop)