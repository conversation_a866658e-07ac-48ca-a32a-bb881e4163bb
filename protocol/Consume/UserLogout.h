﻿#pragma once

#include "Protocol.h"
#include "SHead.h"
#include "STail.h"

#pragma pack(push, 1)

struct UserLogoutReq {
  const static uint16_t MSG_ID = MAKE_MSGID(0x03, 0x03);
  SHead m_head;
  signed char m_token[16];
  STail m_tail;
  NS_ZERO_PROTOCOL(UserLogoutReq, MSG_ID);
};

struct UserLogoutResp {
  const static uint16_t MSG_ID = MAKE_MSGID(0x03, 0x04);
  SHead m_head;
  int m_errcode;
  signed char m_errmsg[32];
  STail m_tail;
  NS_ZERO_PROTOCOL(UserLogoutResp, MSG_ID);
};

#pragma pack(pop)

#ifdef PROTOCOL_STREAM
inline nsStreamWriter &operator<<(nsStreamWriter &stream, const UserLogoutReq &data) {
  stream << data.m_head;

  for (size_t i = 0; i < sizeof(data.m_token); ++i) {
    stream << data.m_token[i];
  }

  stream << data.m_tail;

  return stream;
}

inline nsStreamReader &operator>>(nsStreamReader &stream, UserLogoutReq &data) {
    stream >> data.m_head;

    for (size_t i = 0; i < sizeof(data.m_token); ++i) {
      stream >> data.m_token[i];
    }

    stream >> data.m_tail;

  return stream;
}

inline nsStreamWriter &operator<<(nsStreamWriter &stream, const UserLogoutResp &data) {
    stream << data.m_head;
    stream << data.m_errcode;
    for (size_t i = 0; i < sizeof(data.m_errmsg); ++i) {
      stream << data.m_errmsg[i];
    }
    stream << data.m_tail;

  return stream;
}

inline nsStreamReader &operator>>(nsStreamReader &stream, UserLogoutResp &data) {
    stream >> data.m_head;
    stream >> data.m_errcode;
    for (size_t i = 0; i < sizeof(data.m_errmsg); ++i) {
      stream >> data.m_errmsg[i];
    }
    stream >> data.m_tail;

  return stream;
}
#endif
