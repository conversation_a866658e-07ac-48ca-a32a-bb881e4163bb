#pragma once

#include "Package.h"
#include "Protocol.h"
#include "SHead.h"
#include "STail.h"

#pragma pack(push, 1)

struct PackageModifyReq {
  const static uint16_t MSG_ID = MAKE_MSGID(0x03, 0x0b);
  SHead m_head;
  char m_token[16]; // Token: Authentication token for user/session
  Package m_package;
  STail m_tail;
  NS_ZERO_PROTOCOL(PackageModifyReq, MSG_ID);
};

struct PackageModifyResp {
  const static uint16_t MSG_ID = MAKE_MSGID(0x03, 0x0c);
  SHead m_head;
  int m_errcode;
  char m_errmsg[32];
  STail m_tail;
  NS_ZERO_PROTOCOL(PackageModifyResp, MSG_ID);
};

#pragma pack(pop)