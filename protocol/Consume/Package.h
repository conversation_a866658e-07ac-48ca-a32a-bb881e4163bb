#pragma once
#include "Protocol.h"
#pragma pack(push, 1)

struct Package {
  char m_packageId[16];
  uint32_t m_garageID;        // Garage ID: Unique identifier for the garage
  char m_packageName[64];     // Package name: Name of the package
  uint8_t m_vehicleType;      // Vehicle type: Type of vehicle (e.g., car, bike)
  uint32_t m_powerLimit;      // Power limit: Charging power limit in 0.1W units
  uint8_t m_parkingTimeUnit;  // Parking time unit: Time unit for parking
                              // duration (e.g., minute, hour, month)
  uint8_t m_parkingTimeValue; // Parking time value: Value of the parking
                              // duration (range: [1, 24])
  uint32_t m_totalChargeMinutes; // Total charge minutes: Total allowed charging
                                 // duration in minutes
  uint8_t m_chargeValidityUnit;  // Charge validity unit: Time unit for the
                                // charging validity (e.g., minute, hour, month)
  uint8_t m_chargeValidityValue; // Charge validity value: Value of the charging
  // validity (range: [1, 24])
  NS_ZERO_STRUCT(Package);
};
#pragma pack(pop)