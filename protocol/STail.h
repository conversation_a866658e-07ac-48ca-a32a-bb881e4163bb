#pragma once

#include "Protocol.h"

#pragma pack(push, 1)

struct STail {
  uint16_t m_nFlag; // 标志 (2 bytes)
  uint16_t m_nCrc;  // CRC检验码 (2 bytes)

  STail() : m_nFlag(MESSAGE_TAIL_FLAG) {}
};

#pragma pack(pop)

#ifdef PROTOCOL_STREAM
inline nsStreamWriter &operator<<(nsStreamWriter &stream, const STail &data) {
  stream << data.m_nFlag << data.m_nCrc;

  NS_ASSERT(data.m_nFlag == MESSAGE_TAIL_FLAG);
  return stream;
}

inline nsStreamReader &operator>>(nsStreamReader &stream, STail &data) {
  stream >> data.m_nFlag >> data.m_nCrc;

  NS_ASSERT(data.m_nFlag == MESSAGE_TAIL_FLAG);
  return stream;
}
#endif