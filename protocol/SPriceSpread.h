#pragma once

#include "Protocol.h"
#include "SHead.h"
#include "STail.h"

// 价格差支付请求
// 车库消费->云消费

#pragma pack(push, 1)

struct SPriceSpread {
  const static uint16_t MSG_ID = MAKE_MSGID(0x04, 0x14);

  SHead m_head;
  uint32_t m_CarPortId;     // 车库ID
  uint8_t m_OrderId[25];    // 订单编号
  uint8_t m_PayOrderId[28]; // 交易单号
  uint16_t m_PriceSpread;   // 差额/100
  uint8_t m_PriceType;      // 差额类型

  STail m_tail;

  NS_ZERO_PROTOCOL(SPriceSpread, MSG_ID);
};

#pragma pack(pop)
#ifdef PROTOCOL_STREAM

inline nsStreamWriter &operator<<(nsStreamWriter &stream,
                                  const SPriceSpread &data) {
  stream << data.m_head << data.m_CarPortId;

  for (int i = 0; i < sizeof(data.m_OrderId); ++i) {
    stream << data.m_OrderId[i];
  }

  for (int i = 0; i < sizeof(data.m_PayOrderId); ++i) {
    stream << data.m_PayOrderId[i];
  }

  stream << data.m_PriceSpread << data.m_PriceType << data.m_tail;
  return stream;
}

inline nsStreamReader &operator>>(nsStreamReader &stream, SPriceSpread &data) {

  stream >> data.m_head >> data.m_CarPortId;

  for (int i = 0; i < sizeof(data.m_OrderId); ++i) {
    stream >> data.m_OrderId[i];
  }

  for (int i = 0; i < sizeof(data.m_PayOrderId); ++i) {
    stream >> data.m_PayOrderId[i];
  }

  stream >> data.m_PriceSpread >> data.m_PriceType >> data.m_tail;
  return stream;
}
#endif
