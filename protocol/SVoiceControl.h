#pragma once

#include "Protocol.h"
#include "SHead.h"
#include "STail.h"

#pragma pack(push, 1)

union Data {
  uint32_t price;
  struct {
    uint16_t m_nYear;
    uint8_t m_nMonth;
    uint8_t m_nDay;
  };
};

struct SVoiceControl {
  const static uint16_t MSG_ID = MAKE_MSGID(0x02, 0x18);

  SHead m_head;

  uint8_t m_ServiceId;   // 车库监控服务编号
  uint8_t m_MainBroadId; // 主板编号
  uint8_t m_SubDevId;    // 子设备编号
  uint8_t m_VoiceType;   // 语音类型(1-有效期日期 2-剩余金额)
  Data m_VoiceContain;   // 语音内容(当小于5天才报)

  STail m_tail;

  NS_ZERO_PROTOCOL(SVoiceControl, MSG_ID);
};

#pragma pack(pop)
#ifdef PROTOCOL_STREAM

inline nsStreamWriter &operator<<(nsStreamWriter &stream,
                                  const SVoiceControl &data) {
  stream << data.m_head << data.m_ServiceId << data.m_MainBroadId
         << data.m_SubDevId << data.m_VoiceType << data.m_VoiceContain.price;
  stream << data.m_tail;
  return stream;
}

inline nsStreamReader &operator>>(nsStreamReader &stream, SVoiceControl &data) {
  stream >> data.m_head >> data.m_ServiceId >> data.m_MainBroadId >>
      data.m_SubDevId >> data.m_VoiceType >> data.m_VoiceContain.price;

  stream >> data.m_tail;
  return stream;
}
#endif
