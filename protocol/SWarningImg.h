#pragma once

#include "SHead.h"
#include "STail.h"
#include <vector>

#pragma pack(push, 1)

struct SWarningImg {
  const static uint16_t MSG_ID = MAKE_MSGID(0x02, 0x09);

  SHead m_head;
  uint32_t m_nGarageId; // 车库ID
  uint8_t m_nServerId;  // 车库监控服务ID
  uint8_t m_nBoardId;   // 服务编号（板子编号）
  uint8_t m_nCameraId;  // 摄像头ID
  uint8_t m_szGUID[16]; // 报警ID, 和SWarningImg的guid一致
  uint8_t m_nType;      // 0-无报警 第1位 - 火焰 第2位 - 烟雾 第3位
                        // 温度(一个字节前3位有效）
  uint8_t m_nBlockCount; // 数据段总个数
  uint8_t m_nId;         // 当前数据段序号
  int m_nBlockSize;      // 当前数据段大小
#ifdef PROTOCOL_STREAM
  std::vector<int8_t> m_vData; // 数据段内容
#else
  int8_t *m_vData;
#endif
  STail m_tail;

  NS_ZERO_PROTOCOL(SWarningImg, MSG_ID);
};

#pragma pack(pop)

#ifdef PROTOCOL_STREAM
inline nsStreamWriter &operator<<(nsStreamWriter &stream,
                                  const SWarningImg &data) {
  stream << data.m_head << data.m_nGarageId << data.m_nServerId
         << data.m_nBoardId << data.m_nCameraId;

  for (size_t i = 0; i < sizeof(data.m_szGUID); ++i) {
    stream << data.m_szGUID[i];
  }

  stream << data.m_nType << data.m_nBlockCount << data.m_nId
         << data.m_nBlockSize;
  for (size_t i = 0; i < data.m_nBlockSize; ++i) {
    stream << data.m_vData[i];
  }
  stream << data.m_tail;

  NS_ASSERT(data.m_head.m_nMsgId == data.MSG_ID);
  return stream;
}

inline nsStreamReader &operator>>(nsStreamReader &stream, SWarningImg &data) {
  stream >> data.m_head >> data.m_nGarageId >> data.m_nServerId >>
      data.m_nBoardId >> data.m_nCameraId;

  for (size_t i = 0; i < sizeof(data.m_szGUID); ++i) {
    stream >> data.m_szGUID[i];
  }

  stream >> data.m_nType >> data.m_nBlockCount >> data.m_nId >>
      data.m_nBlockSize;

  data.m_vData.resize(data.m_nBlockSize);
  for (size_t i = 0; i < data.m_nBlockSize; ++i) {
    stream >> data.m_vData[i];
  }
  stream >> data.m_tail;

  NS_ASSERT(data.m_head.m_nMsgId == data.MSG_ID);
  return stream;
}
#endif