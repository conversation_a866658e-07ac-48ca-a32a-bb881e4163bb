﻿#include "Protocol.h"
#include "SHead.h"
#include "STail.h"
#include <vector>

#define MAX_RECT_NUM 8

#pragma pack(push, 1)
struct SWarningInfo {
  uint32_t m_nGarageId; // 车库ID
  uint8_t m_nServerId;  // 车库监控服务ID
  uint8_t m_nBoardId;   // 服务编号（板子编号）
  uint8_t m_nId;        // 摄像头ID
  uint8_t m_szGUID[16];
  uint8_t m_nType; // 0-无报警 第1位 - 火焰 第2位 - 烟雾 第3位 -
                   // 温度(一个字节前3位有效）
  uint64_t m_nAlarmTime; // 报警时间
  uint8_t m_nFireNum;    // 火焰数量
  struct stPos {
    uint16_t m_X; // 火焰距离X
    uint16_t m_Y; // 火焰距离Y
  } m_fire[MAX_RECT_NUM];
};

struct SWarningQueryResult {
  const static uint16_t MSG_ID = MAKE_MSGID(0x02, 0x20);
  SHead  m_head;
  int8_t m_InfoNum;                    // 报警信息数量
  std::vector<SWarningInfo> m_vData; // 数据段内容
  STail m_tail;
  NS_ZERO_PROTOCOL(SWarningQueryResult, MSG_ID);
};

#pragma pack(pop)

#ifdef PROTOCOL_STREAM
inline nsStreamWriter &operator<<(nsStreamWriter &stream,
                                  const SWarningInfo &data) {
  stream << data.m_nGarageId << data.m_nServerId << data.m_nBoardId
         << data.m_nId;
  for (size_t i = 0; i < sizeof(data.m_szGUID); ++i) {
    stream << data.m_szGUID[i];
  }

  stream << data.m_nType << data.m_nAlarmTime << data.m_nFireNum;

  for (int i = 0; i < MAX_RECT_NUM; ++i) {
    stream << data.m_fire[i].m_X << data.m_fire[i].m_Y;
  }

  return stream;
}

inline nsStreamReader &operator>>(nsStreamReader &stream, SWarningInfo &data) {
  stream >> data.m_nGarageId >> data.m_nServerId >> data.m_nBoardId >>
      data.m_nId;
  for (size_t i = 0; i < sizeof(data.m_szGUID); ++i) {
    stream >> data.m_szGUID[i];
  }

  stream >> data.m_nType >> data.m_nAlarmTime >> data.m_nFireNum;

  for (int i = 0; i < MAX_RECT_NUM; ++i) {
    stream >> data.m_fire[i].m_X >> data.m_fire[i].m_Y;
  }

  return stream;
}

inline nsStreamWriter &operator<<(nsStreamWriter &stream,
                                  const SWarningQueryResult &data) {
  stream << data.m_head << data.m_InfoNum;

  for (int i = 0; i < data.m_InfoNum; i++) {
    stream << data.m_vData.at(i);
  }
  stream << data.m_tail;

  NS_ASSERT(data.m_head.m_nMsgId == data.MSG_ID);
  return stream;
}

inline nsStreamReader &operator>>(nsStreamReader &stream,
                                  SWarningQueryResult &data) {
  stream >> data.m_head >> data.m_InfoNum;

  data.m_vData.resize(data.m_InfoNum);

  for (int i = 0; i < data.m_InfoNum; i++) {
    stream >> data.m_vData.at(i);
  }
  stream >> data.m_tail;

  NS_ASSERT(data.m_head.m_nMsgId == data.MSG_ID);
  return stream;
}
#endif
