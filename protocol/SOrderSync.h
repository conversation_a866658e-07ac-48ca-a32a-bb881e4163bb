#pragma once

#include "Protocol.h"
#include "SHead.h"
#include "STail.h"

#pragma pack(push, 1)

// 套餐同步
// 车库消费服务->云消费服务

struct SOrderSync {
  const static uint16_t MSG_ID = MAKE_MSGID(0x04, 0x0C);

  SHead m_head;
  uint8_t m_OrderId[25];      // 订单编号
  uint8_t m_uuid[12];         // NFC UUid
  uint8_t m_UserId[32];       // 用户ID
  uint8_t m_OrderType;        // 订单类型
  uint32_t m_MenuTypeId;      // 套餐模版ID
  uint8_t m_MenuNum;          // 套餐数量
  uint32_t m_CarPort;         // 车库编号
  uint8_t m_MonitorServerNum; // 车库监控服务编号
  uint8_t m_MainBoardNum;     // 主板编号
  uint8_t m_DevNum;           // 设备编号
  uint8_t m_SubDevNum;        // 子设备编号
  int16_t m_DevType;          // 设备类型
  uint8_t m_PayWay;           // 支付方式
  uint8_t m_PayState;         // 支付状态
  uint64_t m_OrderMakeTime;   // 订单创建时间
  uint64_t m_OrderBeginTime;  // 订单开始时间
  uint64_t m_OrderEndTime;    // 订单结束时间
  uint64_t m_ValidTime;       // 订单有效时间
  uint8_t m_OrderState;       // 订单状态
  uint8_t m_DealNum[28];      // 交易单号
  uint32_t m_SerMoney;        // 服务费
  uint32_t m_TotalMoney;      // 总金额
  uint32_t m_RealPayMoney;    // 实际支付金额
  uint32_t m_CharRemainMin;   // 充电剩余分钟数

  STail m_tail;

  NS_ZERO_PROTOCOL(SOrderSync, MSG_ID);
};

struct SPowerSync {
  const static uint16_t MSG_ID = MAKE_MSGID(0x04, 0x2A);

  SHead m_head;
  uint8_t m_PowerLenth; // 功率字节数
  uint16_t m_Power[60]; // 功率列表
  STail m_tail;

  NS_ZERO_PROTOCOL(SPowerSync, MSG_ID);
};

#pragma pack(pop)
#ifdef PROTOCOL_STREAM

inline nsStreamWriter &operator<<(nsStreamWriter &stream,
                                  const SPowerSync &data) {

  stream << data.m_head << data.m_PowerLenth;
  for (int i = 0; i < sizeof(data.m_Power) / sizeof(data.m_Power[0]); i++) {
    stream << data.m_Power[i];
  }

  stream << data.m_tail;
  return stream;
}

inline nsStreamReader &operator>>(nsStreamReader &stream, SPowerSync &data) {

  stream >> data.m_head >> data.m_PowerLenth;
  for (int i = 0; i < sizeof(data.m_Power) / sizeof(data.m_Power[0]); i++) {
    stream >> data.m_Power[i];
  }

  stream >> data.m_tail;
  return stream;
}

inline nsStreamWriter &operator<<(nsStreamWriter &stream,
                                  const SOrderSync &data) {
  stream << data.m_head;

  for (int i = 0; i < sizeof(data.m_OrderId); ++i) {
    stream << data.m_OrderId[i];
  }

  for (int i = 0; i < sizeof(data.m_uuid); ++i) {
    stream << data.m_uuid[i];
  }

  for (int i = 0; i < sizeof(data.m_UserId); ++i) {
    stream << data.m_UserId[i];
  }

  stream << data.m_OrderType << data.m_MenuTypeId << data.m_CarPort
         << data.m_MonitorServerNum << data.m_MainBoardNum << data.m_DevNum
         << data.m_SubDevNum << data.m_DevType << data.m_PayWay
         << data.m_PayState << data.m_OrderMakeTime << data.m_OrderBeginTime
         << data.m_OrderEndTime << data.m_ValidTime;

  stream << data.m_OrderState;

  for (int i = 0; i < sizeof(data.m_DealNum); ++i) {
    stream << data.m_DealNum[i];
  }
  stream << data.m_SerMoney << data.m_TotalMoney << data.m_RealPayMoney
         << data.m_CharRemainMin << data.m_tail;

  return stream;
}

inline nsStreamReader &operator>>(nsStreamReader &stream, SOrderSync &data) {

  stream >> data.m_head;

  for (int i = 0; i < sizeof(data.m_OrderId); ++i) {
    stream >> data.m_OrderId[i];
  }

  for (int i = 0; i < sizeof(data.m_uuid); ++i) {
    stream >> data.m_uuid[i];
  }

  for (int i = 0; i < sizeof(data.m_UserId); ++i) {
    stream >> data.m_UserId[i];
  }
  stream >> data.m_OrderType >> data.m_MenuTypeId >> data.m_MenuNum >>
      data.m_CarPort >> data.m_MonitorServerNum >> data.m_MainBoardNum >>
      data.m_DevNum >> data.m_SubDevNum >> data.m_DevType >> data.m_PayWay >>
      data.m_PayState >> data.m_OrderMakeTime >> data.m_OrderBeginTime >>
      data.m_OrderEndTime >> data.m_ValidTime;

  stream >> data.m_OrderState;

  for (int i = 0; i < sizeof(data.m_DealNum); ++i) {
    stream >> data.m_DealNum[i];
  }
  stream >> data.m_SerMoney >> data.m_TotalMoney >> data.m_RealPayMoney >>
      data.m_CharRemainMin >> data.m_tail;
  return stream;
}
#endif
