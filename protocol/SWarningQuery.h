#pragma once

#include "SHead.h"
#include "STail.h"

#pragma pack(push, 1)
struct SWarningQuery {
  const static uint16_t MSG_ID = MAKE_MSGID(0x02, 0x0c);
  SHead m_head;
  uint8_t m_ServerID;      // 车库监控服务编号
  int64_t m_nDTBegin;      // 起始时间
  int64_t m_nDTEnd;        // 结束时间
  uint8_t m_WarningType;   // 报警类型
  uint32_t m_QueryPageNum; // 查询页数
  uint8_t m_RowNum;        // 每页行数
  STail m_tail;
  NS_ZERO_PROTOCOL(SWarningQuery, MSG_ID);
};
#pragma pack(pop)

#ifdef PROTOCOL_STREAM
inline nsStreamWriter &operator<<(nsStreamWriter &stream,
                                  const SWarningQuery &data) {
  stream << data.m_head << data.m_ServerID << data.m_nDTBegin << data.m_nDTEnd
         << data.m_WarningType << data.m_QueryPageNum << data.m_RowNum
         << data.m_tail;

  return stream;
}

inline nsStreamReader &operator>>(nsStreamReader &stream, SWarningQuery &data) {
  stream >> data.m_head >> data.m_ServerID >> data.m_nDTBegin >>
      data.m_nDTEnd >> data.m_WarningType >> data.m_QueryPageNum >>
      data.m_RowNum >> data.m_tail;

  return stream;
}
#endif