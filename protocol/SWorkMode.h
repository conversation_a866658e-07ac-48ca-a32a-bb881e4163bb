#pragma once

#include "Protocol.h"
#include "SHead.h"
#include "STail.h"

#pragma pack(push, 1)

struct SReturnWorkMode {
  const static uint16_t MSG_ID = MAKE_MSGID(0x02, 0x13);
  SHead m_head;
  uint8_t m_nSolenoidWorkMode; // 0-自动 ，1半自动
  STail m_tail;

  NS_ZERO_PROTOCOL(SReturnWorkMode, MSG_ID);
};

// 电磁阀工作查询
struct SWorkModeQuery {
  const static uint16_t MSG_ID = MAKE_MSGID(0x02, 0x12);
  SHead m_head;
  STail m_tail;

  NS_ZERO_PROTOCOL(SWorkModeQuery, MSG_ID);
};

// 设置电磁阀工作模式
struct SSetWorkMode {
  const static uint16_t MSG_ID = MAKE_MSGID(0x02, 0x14);
  SHead m_head;
  uint8_t m_nSolenoidWorkMode; // 0-自动 ，1半自动
  STail m_tail;

  NS_ZERO_PROTOCOL(SSetWorkMode, MSG_ID);
};

// 设置电磁阀工作模式错误
struct SSetWorkModeError {
  const static uint16_t MSG_ID = MAKE_MSGID(0x02, 0x15);
  SHead m_head;
  uint16_t m_nError;
  STail m_tail;

  NS_ZERO_PROTOCOL(SSetWorkModeError, MSG_ID);
};
#pragma pack(pop)

#ifdef PROTOCOL_STREAM
inline nsStreamWriter &operator<<(nsStreamWriter &stream,
                                  const SReturnWorkMode &data) {
  stream << data.m_head << data.m_nSolenoidWorkMode << data.m_tail;

  NS_ASSERT(data.m_head.m_nMsgId == data.MSG_ID);
  return stream;
}

inline nsStreamReader &operator>>(nsStreamReader &stream,
                                  SReturnWorkMode &data) {
  stream >> data.m_head >> data.m_nSolenoidWorkMode >> data.m_tail;

  return stream;
}

inline nsStreamWriter &operator<<(nsStreamWriter &stream,
                                  const SWorkModeQuery &data) {
  stream << data.m_head << data.m_tail;

  NS_ASSERT(data.m_head.m_nMsgId == data.MSG_ID);
  return stream;
}

inline nsStreamReader &operator>>(nsStreamReader &stream,
                                  SWorkModeQuery &data) {
  stream >> data.m_head >> data.m_tail;

  return stream;
}

inline nsStreamWriter &operator<<(nsStreamWriter &stream,
                                  const SSetWorkMode &data) {
  stream << data.m_head << data.m_nSolenoidWorkMode << data.m_tail;

  NS_ASSERT(data.m_head.m_nMsgId == data.MSG_ID);
  return stream;
}

inline nsStreamReader &operator>>(nsStreamReader &stream, SSetWorkMode &data) {
  stream >> data.m_head >> data.m_nSolenoidWorkMode >> data.m_tail;

  return stream;
}

inline nsStreamWriter &operator<<(nsStreamWriter &stream,
                                  const SSetWorkModeError &data) {
  stream << data.m_head << data.m_nError << data.m_tail;

  NS_ASSERT(data.m_head.m_nMsgId == data.MSG_ID);
  return stream;
}

inline nsStreamReader &operator>>(nsStreamReader &stream,
                                  SSetWorkModeError &data) {
  stream >> data.m_head >> data.m_nError >> data.m_tail;

  return stream;
}
#endif