#pragma once

#include "Protocol.h"
#include "SHead.h"
#include "STail.h"
#include <array>
#include <vector>

// 会员同步请求回复
// 云消费服务->车库消费服务

#pragma pack(push, 1)

// 会员信息
struct St_Vipinfo {
  uint8_t m_NFCId[12];    // NFCid
  uint32_t m_LogicId;     // 逻辑Id
  uint8_t m_UserId[32];   // 用户Id
  uint8_t m_OrderListLen; // 订单有效列表长度
  uint8_t m_OrderList[10][25]; // 一次最多发订单列表(10个)（购买套餐的订单号）

  uint8_t m_IsDisabled; // 是否禁用
};

struct SVipInfoSyncResp {
  const static uint16_t MSG_ID = MAKE_MSGID(0x04, 0x0F);

  SHead m_head;
  uint8_t m_Syncflag;            // 结束标志（1--开始，0--进行中）
  uint16_t m_NowSyncNum;         // 当前包数
  St_Vipinfo m_ArrayVipInfo[10]; // 会员信息数组
  STail m_tail;

  NS_ZERO_PROTOCOL(SVipInfoSyncResp, MSG_ID);
};

#pragma pack(pop)
#ifdef PROTOCOL_STREAM

inline nsStreamWriter &operator<<(nsStreamWriter &stream,
                                  const St_Vipinfo &data) {

  for (int i = 0; i < sizeof(data.m_NFCId); ++i) {
    stream << data.m_NFCId[i];
  }

  stream << data.m_LogicId;
  for (int i = 0; i < sizeof(data.m_UserId); ++i) {
    stream << data.m_UserId[i];
  }

  stream << data.m_OrderListLen;

  // 根据实际的订单列表长度写入 m_OrderList
  for (int i = 0; i < 25; ++i) {
    for (int j = 0; j < 10; ++j) {
      stream << data.m_OrderList[i][j];
    }
  }

  return stream;
}

inline nsStreamReader &operator>>(nsStreamReader &stream, St_Vipinfo &data) {

  for (int i = 0; i < sizeof(data.m_NFCId); ++i) {
    stream >> data.m_NFCId[i];
  }

  stream >> data.m_LogicId;
  for (int i = 0; i < sizeof(data.m_UserId); ++i) {
    stream >> data.m_UserId[i];
  }

  stream >> data.m_OrderListLen;

  // 根据实际的订单列表长度写入 m_OrderList
  for (int i = 0; i < 10; ++i) {
    for (int j = 0; j < 25; ++j) {
      stream >> data.m_OrderList[i][j];
    }
  }
  return stream;
}

inline nsStreamWriter &operator<<(nsStreamWriter &stream,
                                  const SVipInfoSyncResp &data) {
  stream << data.m_head << data.m_Syncflag << data.m_NowSyncNum;

  for (int i = 0; i < 10; ++i) {
    stream << data.m_ArrayVipInfo[i];
  }
  stream << data.m_tail;
  NS_ASSERT(data.m_head.m_nMsgId == data.MSG_ID);

  return stream;
}

inline nsStreamReader &operator>>(nsStreamReader &stream,
                                  SVipInfoSyncResp &data) {
  stream >> data.m_head >> data.m_Syncflag >> data.m_NowSyncNum;

  for (int i = 0; i < 10; ++i) {
    stream >> data.m_ArrayVipInfo[i];
  }
  stream >> data.m_tail;
  NS_ASSERT(data.m_head.m_nMsgId == data.MSG_ID);
  return stream;
}
#endif
