#pragma once

#include "Protocol.h"
#include "SHead.h"
#include "STail.h"

#pragma pack(push, 1)

struct SRegister {
  const static uint16_t MSG_ID = MAKE_MSGID(0x01, 0x01);

  SHead m_head;
  uint32_t m_nNum;
  uint32_t m_nVersion;
  STail m_tail;

  NS_ZERO_PROTOCOL(SRegister, MSG_ID);
};
#pragma pack(pop)

#ifdef PROTOCOL_STREAM
inline nsStreamWriter &operator<<(nsStreamWriter &stream,
                                  const SRegister &data) {
  stream << data.m_head << data.m_nNum << data.m_nVersion << data.m_tail;

  NS_ASSERT(data.m_head.m_nMsgId == data.MSG_ID);
  return stream;
}

inline nsStreamReader &operator>>(nsStreamReader &stream, SRegister &data) {
  stream >> data.m_head >> data.m_nNum >> data.m_nVersion >> data.m_tail;

  NS_ASSERT(data.m_head.m_nMsgId == data.MSG_ID);
  return stream;
}
#endif