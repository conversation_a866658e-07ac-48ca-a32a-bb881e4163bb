#pragma once

#include "Protocol.h"
#include "SHead.h"
#include "STail.h"

#pragma pack(push, 1)

struct SVoiceControlResp {
  const static uint16_t MSG_ID = MAKE_MSGID(0x02, 0x19);

  SHead m_head;

  uint8_t m_ServiceId;   // 车库监控服务编号
  uint8_t m_MainBroadId; // 主板编号
  uint8_t m_SubDevId;    // 子设备编号
  uint16_t m_Result;     // 执行结果

  STail m_tail;

  NS_ZERO_PROTOCOL(SVoiceControlResp, MSG_ID);
};

#pragma pack(pop)
#ifdef PROTOCOL_STREAM

inline nsStreamWriter &operator<<(nsStreamWriter &stream,
                                  const SVoiceControlResp &data) {
  stream << data.m_head << data.m_ServiceId << data.m_MainBroadId
         << data.m_SubDevId << data.m_Result;
  stream << data.m_tail;
  return stream;
}

inline nsStreamReader &operator>>(nsStreamReader &stream,
                                  SVoiceControlResp &data) {
  stream >> data.m_head >> data.m_ServiceId >> data.m_MainBroadId >>
      data.m_SubDevId >> data.m_Result;

  stream >> data.m_tail;
  return stream;
}
#endif
