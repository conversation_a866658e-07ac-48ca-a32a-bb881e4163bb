#pragma once

#include "SHead.h"
#include "STail.h"

#pragma pack(push, 1)
struct SUUIDGarageInfoQuery {
  const static uint16_t MSG_ID = MAKE_MSGID(0x02, 0x27);

  SHead m_head;
  int8_t m_szUUID[12];
  STail m_tail;

  NS_ZERO_PROTOCOL(SUUIDGarageInfoQuery, MSG_ID);
};
#pragma pack(pop)

#ifdef PROTOCOL_STREAM
inline nsStreamWriter &operator<<(nsStreamWriter &stream,
                                  const SUUIDGarageInfoQuery &data) {
  stream << data.m_head;

  for (size_t i = 0; i < sizeof(data.m_szUUID); ++i) {
    stream << data.m_szUUID[i];
  }
  stream << data.m_tail;

  NS_ASSERT(data.m_head.m_nMsgId == data.MSG_ID);
  return stream;
}

inline nsStreamReader &operator>>(nsStreamReader &stream,
                                  SUUIDGarageInfoQuery &data) {
  stream >> data.m_head;

  for (size_t i = 0; i < sizeof(data.m_szUUID); ++i) {
    stream >> data.m_szUUID[i];
  }
  stream >> data.m_tail;

  NS_ASSERT(data.m_head.m_nMsgId == data.MSG_ID);
  return stream;
}
#endif