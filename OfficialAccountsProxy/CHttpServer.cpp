#include "CHttpServer.h"
#include "HttpCommonFunc.h"
#include "NSMarco.h"
#include "common/KLog.h"
#include "handler/COfficicalAccountHttpHandler.h"
#include <Poco/DateTimeFormatter.h>
#include <Poco/LocalDateTime.h>
#include <Poco/Timestamp.h>
#include <nlohmann/json.hpp>

#define REQ_TIMEOUT 5 * 1000 * 1000

#define GARAGE_INFO_URL "http://127.0.0.1:8090/cloudgarage/warn-svr/garage-info"

#define GARAGE_ORDER_UNIONID_URL                                               \
  "https://127.0.0.1:8444/consume/package/active/query"

static std::map<uint32_t, NJson> g_mGarageInfo;

std::string warnType2Str(uint8_t nType) {
  std::string sWarnType;
  if (nType & 0x01) {
    sWarnType += "您的电瓶车所在停车棚发生火情，请查看";
    return sWarnType;
  }

  if ((nType >> 1) & 0x01) {
    if (!sWarnType.empty()) {
      sWarnType += "/";
    }
    sWarnType += "您的电瓶车所在停车棚疑似烟雾预警，请查看";
    return sWarnType;
  }

  if ((nType >> 2) & 0x01) {
    if (!sWarnType.empty()) {
      sWarnType += "/";
    }
    sWarnType += "您的电瓶车所在停车棚疑似高温预警，请查看";
    return sWarnType;
  }

  if ((nType >> 3) & 0x01) {
    if (!sWarnType.empty()) {
      sWarnType += "/";
    }
    sWarnType += "有人跌倒，请查看";
    return sWarnType;
  }

  return sWarnType;
}

CHttpServer::CHttpServer()
    : m_pHttpSvr(nullptr), m_pApp(nullptr), m_pLoop(nullptr), m_nId(0) {
  CWxHttpHandler::initWxTokenClient();
  CWxHttpHandler::updateWxToken();
}

CHttpServer::~CHttpServer() {
  if (m_pHttpSvr != nullptr) {
    m_pHttpSvr->stop();
  }
}

void CHttpServer::setApp(koala::IKApplication *pApp) { m_pApp = pApp; }

void CHttpServer::setEventLoop(koala::IKEventLoop::Ptr pLoop) {
  if (m_pLoop != nullptr) {
    return;
  }

  m_pLoop = pLoop;

  m_pLoop->setInterval(1000 * 60 * 60 * 2, [this](koala::KTimerID timerID) {
    CWxHttpHandler::updateWxToken();
  });
}

void CHttpServer::setModuleId(uint32_t nId) { m_nId = nId; }

bool CHttpServer::start() {
  if (m_pLoop != nullptr) {
    m_pLoop->setTimeout(1000, [this](koala::KTimerID timerID) {
      std::lock_guard<std::mutex> locker(m_mutex);
      for (auto itor = m_mWriter.begin(); itor != m_mWriter.end(); ++itor) {
        Poco::Timestamp now;
        if (now.epochMicroseconds() - itor->second.second >= REQ_TIMEOUT) {
          httpSend(ECODE_GARAGE_REQ_TIMEOUT, itor->first);
          m_mWriter.erase(itor);
        }
      }
    });
  }

  run();
  return true;
}

void CHttpServer::stop() {
  if (m_pHttpSvr != nullptr) {
    m_pHttpSvr->stop();
  }
}

void CHttpServer::sendPluginData(uint32_t nChannelId, const char *pData,
                                 int nLen) {
  auto buffer =
      m_pApp->getMemoryManager()->alloc(sizeof(koala::KQueueMessage) + nLen);
  koala::KQueueMessage::Ptr pPluginMsg = koala::KCreateQueueMessage(buffer);
  memcpy(pPluginMsg->_msgData, pData, nLen);

  pPluginMsg->_msgLen = nLen;
  pPluginMsg->_srcId = m_nId;
  pPluginMsg->_msgType = KMSG_TYPE_PLUGIN;
  pPluginMsg->_msgId = nChannelId;
  m_pApp->getQueueService()->send(pPluginMsg);
}

void CHttpServer::dealResp(uint16_t nMsgId, const nlohmann::json &jObj) {
  switch (nMsgId) {
  case OFFICICAL_ACCOUNT_WARN_PUSH_REQ:
    onWarnPushReq(jObj);
    break;
  case OFFICICAL_ACCOUNT_SOCKET_STATUS_PUSH_REQ:
    onSocketStatusPushReq(jObj);
    break;
  default:
    onHttpModuleResp(jObj);
    break;
  }
}

void CHttpServer::reqAsync(int64_t nTimestamp, std::string &sProtocolUUID,
                           const koala::IKHttpWriter::Ptr &writer) {
  std::lock_guard<std::mutex> locker(m_mutex);
  m_mWriter[writer] = std::make_pair(sProtocolUUID, nTimestamp);
}

std::string CHttpServer::wxToken() const { return CWxHttpHandler::wxToken(); }

void CHttpServer::run() {
  if (m_pHttpSvr != nullptr) {
    return;
  }

  m_pRouter = koala::KCreateHttpRouter();
  m_pRouter->onHttpMiddleware(
      [](koala::IKHttpRequest *req, koala::IKHttpResponse *resp) {
        KCONSOLE_INFO("middle ware req %s", req->getUrl().c_str());
        resp->setHeader("Server", "koala server");
        return KHTTP_STATUS_NEXT;
      });

  // m_pRouter->render("/", "./html");

  m_pRouter->onHttpAny("/officical/official-accounts",
                       COfficicalAccountHttpHandler::onOfficialAccountReq);

  m_pRouter->onHttpError(
      [&](koala::IKHttpRequest *req, koala::IKHttpResponse *resp) -> int {
        auto status = resp->getStatusCode();
        if (status == KHTTP_STATUS_NOT_FOUND) {
          resp->sendJson("{\"result\":\"not found\"}");
        } else if (status == KHTTP_STATUS_METHOD_NOT_ALLOWED) {
          resp->sendJson("{\"result\":\"not allow\"}");
        }
        return 0;
      });

  m_pHttpSvr = koala::KCreateHttpServer();
  m_pHttpSvr->setWithTLS("./cert/server.crt", "./cert/server.key");
  m_pHttpSvr->setRouter(m_pRouter);
  m_pHttpSvr->setPort(10080, 10443);
  m_pHttpSvr->run(nullptr, false);
}

void CHttpServer::onHttpModuleResp(const nlohmann::json &jObj) {
  std::lock_guard<std::mutex> locker(m_mutex);
  auto sProtocolUUID = jObj["p_uuid"].get<std::string>();
  for (auto itor = m_mWriter.begin(); itor != m_mWriter.end();) {
    auto pair = itor->second;
    if (pair.first == sProtocolUUID) {
      httpSendResp(itor->first, jObj);
      itor = m_mWriter.erase(itor);
    } else {
      ++itor;
    }
  }
}

void CHttpServer::onWarnPushReq(const nlohmann::json &jObj) {
  auto nGarageId = jObj["garage_id"].get<uint32_t>();
  NJson jGarageInfo = NJson::object();
  // if (g_mGarageInfo.find(nGarageId) == g_mGarageInfo.end()) {
  NJson jReq;
  jReq["garage_ids"].push_back(nGarageId);
  auto jGarageInfos = CHttpServer::getGarageInfo(jReq.dump());
  if (jGarageInfos.find("garages") != jGarageInfos.end() &&
      jGarageInfos["garages"].size() > 0) {
    jGarageInfo = jGarageInfos["garages"][0];
    g_mGarageInfo[nGarageId] = jGarageInfo;
  }
  // }

  std::string sGarageName;
  std::string sCommunityName;
  if (jGarageInfo.find("CommunityName") != jGarageInfo.end()) {
    sCommunityName = jGarageInfo["CommunityName"].get<std::string>();
  }

  if (jGarageInfo.find("Name") != jGarageInfo.end()) {
    sGarageName = jGarageInfo["Name"].get<std::string>();
  }

  auto sPos = sCommunityName + sGarageName + std::to_string(nGarageId);
  sPos += "-" + std::to_string(jObj["server_id"].get<uint8_t>());
  sPos += "-" + std::to_string(jObj["board_id"].get<uint8_t>());
  sPos += "-" + std::to_string(jObj["camera_id"].get<uint8_t>());
  NJson jPos;
  jPos["value"] = sPos;
  jPos["color"] = "#173177";

  Poco::LocalDateTime now;
  // 格式化为指定格式
  std::string sDT = Poco::DateTimeFormatter::format(now, "%Y年%m月%d日");

  NJson jTime;
  jTime["value"] = sDT;
  jTime["color"] = "#173177";

  NJson jType;
  jType["value"] = warnType2Str(jObj["type"].get<uint8_t>());
  jType["color"] = "#173177";

  auto sFilePath = jObj["image"].get<std::string>();
  NJson jWarn;
  jWarn["users"] = jObj["open_ids"];
  jWarn["pos"] = jPos;
  jWarn["type"] = jType;
  jWarn["DT"] = jTime;
  jWarn["image"] = sFilePath;
  CWxHttpHandler wxHandler;
  wxHandler.pushWarn(jWarn);
}

void CHttpServer::onSocketStatusPushReq(const nlohmann::json &jObj) {
  auto nGarageId = jObj["garage_id"].get<uint32_t>();
  NJson jGarageInfo = NJson::object();
  // if (g_mGarageInfo.find(nGarageId) == g_mGarageInfo.end()) {
  NJson jReq;
  jReq["garage_ids"].push_back(nGarageId);
  auto jGarageInfos = CHttpServer::getGarageInfo(jReq.dump());
  if (jGarageInfos.find("garages") != jGarageInfos.end() &&
      jGarageInfos["garages"].size() > 0) {
    jGarageInfo = jGarageInfos["garages"][0];
    g_mGarageInfo[nGarageId] = jGarageInfo;
  }
  // }

  std::string sGarageName;
  std::string sCommunityName;
  if (jGarageInfo.find("CommunityName") != jGarageInfo.end()) {
    sCommunityName = jGarageInfo["CommunityName"].get<std::string>();
  }

  if (jGarageInfo.find("Name") != jGarageInfo.end()) {
    sGarageName = jGarageInfo["Name"].get<std::string>();
  }

  auto sPos = sCommunityName + sGarageName;
  std::string sDev = std::to_string(nGarageId);
  sDev += "-" + std::to_string(jObj["server_id"].get<uint8_t>());
  sDev += "-" + std::to_string(jObj["board_id"].get<uint8_t>());
  sDev += "-" + std::to_string(jObj["dev_id"].get<uint8_t>());
  sDev += "-" + std::to_string(jObj["sub_id"].get<uint8_t>());

  NJson jPos;
  jPos["value"] = sPos;
  jPos["color"] = "#173177";

  NJson jDev;
  jDev["value"] = sDev;
  jDev["color"] = "#173177";

  Poco::LocalDateTime now;
  // 格式化为指定格式
  std::string sDT = Poco::DateTimeFormatter::format(now, "%Y年%m月%d日");

  NJson jTime;
  jTime["value"] = sDT;
  jTime["color"] = "#173177";

  NJson jStatus;
  jStatus["value"] = jObj["status"].get<uint8_t>() == 0 ? "您的电瓶车结束充电"
                                                        : "您的电瓶车开始充电";
  jStatus["color"] = "#173177";

  NJson jStatusChanged;
  jStatusChanged["users"] = jObj["open_ids"]; //
  // 消费服务器返回的用户unionid,需要转换成openId
  jStatusChanged["pos"] = jPos;
  jStatusChanged["dev"] = jDev;
  jStatusChanged["status"] = jStatus;
  jStatusChanged["DT"] = jTime;

  CWxHttpHandler wxHandler;
  wxHandler.pushSocketStatusChanged(jStatusChanged);
}

nlohmann::json CHttpServer::getGarageInfo(const std::string &body) {
  auto resp = koala::KHttpsRequestPost(
      GARAGE_INFO_URL, body, "./cert/server.crt", "./cert/server.key");

  if (resp->getStatusCode() == KHTTP_STATUS_OK) {
    try {
      NJson jGarageInfo = NJson::parse(resp->getBody());
      return jGarageInfo;
    } catch (std::exception &e) {
      klog_error("CHttpServer::getGarageInfo %s json: %s", e.what(),
                 resp->getBody().c_str());
    }
  }

  return NJson();
}

nlohmann::json CHttpServer::getGarageOrderUnionId(const std::string &body) {
  auto resp = koala::KHttpsRequestPost(
      GARAGE_ORDER_UNIONID_URL, body, "./cert/server.crt", "./cert/server.key");

  if (resp->getStatusCode() == KHTTP_STATUS_OK) {
    NJson jGarageInfo = NJson::parse(resp->getBody());
    return jGarageInfo;
  }

  return NJson();
}
