#pragma once
#include "EErrorCode.h"
#include "net/http/IKHttpRouter.h"
#include "nlohmann/json.hpp"

inline void httpSend(int errcode, const koala::IKHttpWriter::Ptr &writer) {
  nlohmann::json respJson;
  respJson["errcode"] = errcode;
  respJson["errmsg"] = getErrorMessage(errcode);
  auto resp_string = respJson.dump();
  writer->begin();
  writer->writeHeader("Server", koala::KHttpVersoin());
  writer->writeHeader("Content-Type", "application/json");
  writer->writeHeader("Connection", "close");
  writer->writeStatus(KHTTP_STATUS_OK);
  writer->writeBody(resp_string.c_str(), resp_string.length());
  writer->end();
}

inline void httpSend(int errcode, koala::IKHttpResponse *resp) {
  nlohmann::json respJson;
  respJson["errcode"] = errcode;
  respJson["errmsg"] = getErrorMessage(errcode);
  auto resp_string = respJson.dump();
  resp->sendString(resp_string);
}

inline void httpSendResp(const koala::IKHttpWriter::Ptr &writer,
                         const nlohmann::json &msgJson) {
  auto resp = msgJson.dump();
  writer->begin();
  writer->writeHeader("server", koala::KHttpVersoin());
  writer->writeHeader("Content-Type", "application/json");
  writer->writeHeader("Connection", "close");
  writer->writeStatus(KHTTP_STATUS_OK);
  writer->writeBody(resp.c_str(), resp.length());
  writer->end();
}

inline void httpSendResp(koala::IKHttpResponse *resp,
                         const nlohmann::json &msgJson) {
  auto sResp = msgJson.dump();
  resp->sendString(sResp);
}