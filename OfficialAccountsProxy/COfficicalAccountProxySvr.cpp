#include "COfficicalAccountProxySvr.h"
#include "CHttpServer.h"
#include "CommonFunc.h"
#include "EErrorCode.h"
#include "NSMarco.h"
#include "common/KLog.h"
#include "core/IKApplication.h"
#include "nlohmann/json.hpp"
#include <iomanip>
#include <iostream>

using NJson = nlohmann::json;

COfficicalAccountProxySvr::COfficicalAccountProxySvr(koala::IKApplication *app)
    : koala::IKBaseService(app) {}

COfficicalAccountProxySvr::~COfficicalAccountProxySvr() {}

const char *COfficicalAccountProxySvr::name() const {
  return "OfficicalAccountProxy";
}

bool COfficicalAccountProxySvr::initialize() {
  _app->getQueueService()->subMessage(OFFICICAL_ACCOUNT_TCP_2_HTTP_CHANNEL,
                                      this);

  CSington<CHttpServer>::ins()->setApp(_app);
  CSington<CHttpServer>::ins()->setModuleId(_id);
  CSington<CHttpServer>::ins()->setEventLoop(_loop);
  return CSington<CHttpServer>::ins()->start();
}

void COfficicalAccountProxySvr::uninitialize() {
  CSington<CHttpServer>::ins()->stop();
}

void COfficicalAccountProxySvr::onQueueMessage(koala::KQueueMessage::Ptr msg) {
  if (msg->_msgType == KMSG_TYPE_TCP_SERVER) {
    auto tcpMsg = reinterpret_cast<koala::IKTcpMessage *>(msg->_msgData);
    if (tcpMsg->_msgType == KMSG_TCP_ONLINE) {
      std::cout << "tcp online " << tcpMsg->_peerAddr << std::endl;
    } else if (tcpMsg->_msgType == KMSG_TCP_OFFLINE) {
      std::cout << "tcp offline " << tcpMsg->_peerAddr << std::endl;
    } else if (tcpMsg->_msgType == KMSG_TCP_DATA) {
      const unsigned char *p =
          reinterpret_cast<const unsigned char *>(tcpMsg->_pData);
      for (auto i = 0; i < tcpMsg->_dataLen; i++) {
        std::cout << std::hex << std::setw(2) << std::setfill('0')
                  << static_cast<int>(p[i]) << " ";
      }
      std::cout << std::endl;
    }
  } else if (msg->_msgType == KMSG_TYPE_PLUGIN) {
    if (msg->_msgId ==
        OFFICICAL_ACCOUNT_TCP_2_HTTP_CHANNEL) { // http与tcp间通信
      onOfficicalAccountPush(msg->_msgData, msg->_msgLen);
    }
  }
}

void COfficicalAccountProxySvr::onOfficicalAccountPush(int8_t *pData,
                                                       uint32_t nLen) {
  std::string sJson((char *)pData, nLen);
  try {
    auto jResp = NJson::parse(sJson);
    auto nMsgId = 0;
    if (jResp.find("msgId") != jResp.end()) {
      nMsgId = jResp["msgId"].get<uint16_t>();
    }
    CSington<CHttpServer>::ins()->dealResp(nMsgId, jResp);
  } catch (NJson::exception &e) {
    klog_error(
        "COfficicalAccountProxySvr::onOfficicalAccountPush: %s\r Json: %s",
        e.what(), sJson.c_str());
  }
}

KSERVICE_EXPORT(COfficicalAccountProxySvr)