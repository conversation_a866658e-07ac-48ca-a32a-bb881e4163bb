#pragma once

#include "core/IKBaseService.h"
#include "net/http/IKHttpServer.h"

class COfficicalAccountProxySvr : public koala::IKBaseService {
public:
  explicit COfficicalAccountProxySvr(koala::IKApplication *app);
  virtual ~COfficicalAccountProxySvr();

  // IKService
  virtual const char *name() const override;
  virtual bool initialize() override;
  virtual void uninitialize() override;

  // IQUeueSink
  virtual void onQueueMessage(koala::KQueueMessage::Ptr msg);

private:
  void onOfficicalAccountPush(int8_t *pData, uint32_t nLen);
};