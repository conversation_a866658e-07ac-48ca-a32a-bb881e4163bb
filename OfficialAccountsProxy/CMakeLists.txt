cmake_minimum_required(VERSION 3.16)

project(OfficialAccountsProxy)

if(WIN32)
    set(LIBRARY_OUTPUT_PATH "${CMAKE_BINARY_DIR}/bin")
else()
    set(LIBRARY_OUTPUT_PATH "${CMAKE_BINARY_DIR}/../run/plugins")
endif()

aux_source_directory(. mainSrcCode)
aux_source_directory(./handler handlerSrcCode)

add_library(OfficialAccountsProxy SHARED ${mainSrcCode} ${handlerSrcCode})

target_link_libraries(OfficialAccountsProxy koala PocoFoundation PocoXML)