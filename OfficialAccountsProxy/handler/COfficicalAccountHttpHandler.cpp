#include "COfficicalAccountHttpHandler.h"
#include "../CHttpServer.h"
#include "NSMarco.h"
#include "common/KLog.h"
#include "common/Utils.h"
#include <Poco/AutoPtr.h>
#include <Poco/DOM/DOMParser.h>
#include <Poco/DOM/Document.h>
#include <Poco/DOM/Element.h>
#include <Poco/DOM/Node.h>
#include <Poco/DOM/NodeList.h>
#include <Poco/DOM/Text.h>
#include <Poco/DigestStream.h>
#include <Poco/Exception.h>
#include <Poco/SAX/InputSource.h>
#include <Poco/SHA1Engine.h>
#include <Poco/Timestamp.h>
#include <iomanip>
#include <nlohmann/json.hpp>
#include <sstream>

#define TOKEN "twdz688511"
#include <set>

int COfficicalAccountHttpHandler::onOfficialAccountReq(
    const koala::IKHttpRequest::Ptr &req,
    const koala::IKHttpWriter::Ptr &writer) {
  std::string sType = req->getMethod();
  if (sType == "GET") {
    return onCheck(req, writer);
  } else if (sType == "POST") {
    return onPost(req, writer);
  }

  writer->begin();
  writer->end("error");
  return 0;
}

int COfficicalAccountHttpHandler::onCheck(
    const koala::IKHttpRequest::Ptr &req,
    const koala::IKHttpWriter::Ptr &writer) {
  auto sSignature = req->getParam("signature");
  auto sEchostr = req->getParam("echostr");
  std::set<std::string> setInfo;
  setInfo.insert(TOKEN);
  setInfo.insert(req->getParam("timestamp"));
  setInfo.insert(req->getParam("nonce"));

  std::string sCheck;
  for (const auto &s : setInfo) {
    sCheck += s;
  }

  // 创建 SHA1Engine 对象
  Poco::SHA1Engine sha1Engine;

  // 写入数据
  sha1Engine.update(sCheck);

  // 获取摘要
  const Poco::DigestEngine::Digest &digest = sha1Engine.digest();

  // 将摘要转换为十六进制字符串

  std::ostringstream oss;
  for (const auto &byte : digest) {
    oss << std::hex << std::setfill('0') << std::setw(2)
        << (int)(unsigned char)byte;
  }

  std::string sDigest = oss.str();

  writer->begin();
  if (sDigest == sSignature) {
    return writer->end(sEchostr.c_str());
  }

  return writer->end("error");
}

void parseXML(const std::string &xmlData,
              std::map<std::string, std::string> &mValue) {
  try {
    // 创建DOM解析器
    Poco::XML::DOMParser parser;

    // 解析XML数据
    std::istringstream input(xmlData);
    Poco::XML::InputSource source(input);
    Poco::AutoPtr<Poco::XML::Document> doc = parser.parse(&source);

    // 提取字段
    Poco::AutoPtr<Poco::XML::Element> root = doc->documentElement();
    if (!root) {
      klog_error("%s %d Invalid XML structure.", __FILE__, __LINE__);
      return;
    }

    // 提取具体字段
    auto getTextContent = [&mValue](Poco::XML::Element *element) -> void {
      Poco::AutoPtr<Poco::XML::NodeList> nodes = element->childNodes();
      if (nodes->length() > 0) {
        Poco::XML::Node *node = nodes->item(0);
        while (node != nullptr) {
          if (node && node->firstChild()) {
            mValue[node->nodeName()] = node->firstChild()->getNodeValue();
          }
          node = node->nextSibling();
        }
      }
    };

    getTextContent(root);

  } catch (const Poco::Exception &ex) {
    klog_error("%s %d Error parsing XML: %s", __FILE__, __LINE__,
               ex.displayText().c_str());
  }
}

int COfficicalAccountHttpHandler::onPost(
    const koala::IKHttpRequest::Ptr &req,
    const koala::IKHttpWriter::Ptr &writer) {
  auto sBody = req->getBody();

  std::map<std::string, std::string> mValue;
  parseXML(sBody, mValue);

  for (const auto &pair : mValue) {
    klog_info("%s : %s", pair.first.c_str(), pair.second.c_str());
  }

  if (mValue.find("MsgType") != mValue.end()) {
    if (mValue["MsgType"] == "text") {
      auto fromusername = mValue["FromUserName"];
      auto tousername = mValue["ToUserName"];
      // 单位为秒，不是毫秒
      auto DT = Poco::Timestamp().epochMicroseconds() / 1000 / 1000;

      char sz[255] = {0};
      sprintf(sz, "<CreateTime><![CDATA[%ld]]></CreateTime>", DT);

      std::string content = "你好，感谢关注！";
      std::string text;
      text.append("<xml>");
      text.append("<ToUserName><![CDATA[" + fromusername + "]]></ToUserName>");
      text.append("<FromUserName><![CDATA[" + tousername +
                  "]]></FromUserName>");
      text.append(sz);
      text.append("<MsgType><![CDATA[text]]></MsgType>");
      text.append("<Content><![CDATA[" + content + "]]></Content>");
      text.append("</xml>");

      writer->begin();
      writer->end(text.c_str());
    } else if (mValue["MsgType"] == "event") {
      auto sEvent = mValue["Event"];

      if (sEvent == std::string("subscribe")) { // 关注
        return onSubscribe(req, writer, mValue);
      } else if (sEvent == std::string("unsubscribe")) { // 取消关注
        return onUnsubscribe(req, writer, mValue);
      }else if(sEvent == std::string("TEMPLATESENDJOBFINISH")){
        /*
        微信服务器在模板消息发送后会向开发者服务器推送该事件，开发者需在5秒内返回特定格式的响应13
        若超时未响应，微信会重试请求并记录499错误（nginx常见）
        */
        std::string text;
        text.append("<xml>");
        text.append("<ToUserName><![CDATA[" + mValue["FromUserName"] +
                    "]]></ToUserName>");
        text.append("<FromUserName><![CDATA[" + mValue["ToUserName"] +
                    "]]></FromUserName>");
        text.append("<CreateTime>" + std::to_string(Poco::Timestamp().epochMicroseconds() / 1000 / 1000) +"</CreateTime>");
        text.append("<MsgType><![CDATA[text]]></MsgType>");
        text.append("<Content><![CDATA[success]]></Content>");
        text.append("</xml>");
        writer->begin();
        writer->end(text.c_str());
      }
    }
  }

  return 0;
}

int COfficicalAccountHttpHandler::onSubscribe(
    const koala::IKHttpRequest::Ptr &req,
    const koala::IKHttpWriter::Ptr &writer,
    std::map<std::string, std::string> &mValue) {

  auto sOpenId = mValue["FromUserName"];

  CWxHttpHandler wxHandler;
  auto sUnionId = wxHandler.getWxUnionId(sOpenId);
  mValue["unionid"] = sUnionId;

  dealReq(OFFICICAL_ACCOUNT_ADD_REQ, mValue, writer);

  return 0;
}

int COfficicalAccountHttpHandler::onUnsubscribe(
    const koala::IKHttpRequest::Ptr &req,
    const koala::IKHttpWriter::Ptr &writer,
    std::map<std::string, std::string> &mValue) {

  dealReq(OFFICICAL_ACCOUNT_DEL_REQ, mValue, writer);

  return 0;
}

void COfficicalAccountHttpHandler::dealReq(
    uint16_t nMsgId, const std::map<std::string, std::string> &mValue,
    const koala::IKHttpWriter::Ptr &writer) {

  auto sProtocolUUID = koala::KCreateUUID();
  // 分页查询车库
  nlohmann::json jReq = mValue;
  jReq["msgId"] = nMsgId;
  jReq["p_uuid"] = sProtocolUUID;

  Poco::Timestamp now;
  CSington<CHttpServer>::ins()->reqAsync(now.epochMicroseconds(), sProtocolUUID,
                                         writer);
  auto sData = jReq.dump();
  CSington<CHttpServer>::ins()->sendPluginData(
      OFFICICAL_ACCOUNT_TCP_2_HTTP_CHANNEL, sData.c_str(), sData.length());
}