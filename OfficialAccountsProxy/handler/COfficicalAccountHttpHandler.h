#pragma once

#include "net/http/IKHttpRequest.h"
#include "net/http/IKHttpWriter.h"
#include <condition_variable>

class COfficicalAccountHttpHandler {
public:
  // 车库列表获取
  static int onOfficialAccountReq(const koala::IKHttpRequest::Ptr &req,
                                  const koala::IKHttpWriter::Ptr &writer);

  static int onCheck(const koala::IKHttpRequest::Ptr &req,
                     const koala::IKHttpWriter::Ptr &writer);

  static int onPost(const koala::IKHttpRequest::Ptr &req,
                    const koala::IKHttpWriter::Ptr &writer);

  static int onSubscribe(const koala::IKHttpRequest::Ptr &req,
                         const koala::IKHttpWriter::Ptr &writer,
                         std::map<std::string, std::string> &mValue);

  static int onUnsubscribe(const koala::IKHttpRequest::Ptr &req,
                           const koala::IKHttpWriter::Ptr &writer,
                           std::map<std::string, std::string> &mValue);

  static void dealReq(uint16_t nMsgId,
                      const std::map<std::string, std::string> &mValue,
                      const koala::IKHttpWriter::Ptr &writer);
};