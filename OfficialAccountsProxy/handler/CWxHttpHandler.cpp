#include "CWxHttpHandler.h"
#include "../HttpCommonFunc.h"
#include "EErrorCode.h"
#include "common/KLog.h"
#include "nlohmann/json.hpp"

// #define TestTemplate
#ifdef TestTemplate
#define APP_ID "wxf623a73c3d22f202"
#define APP_SECRET "485758ca7747a0869c66444207aa00e3"
#define WARN_TEMPLATE_ID "qcvWufTyZgkQ6HvWv2FkGir7d4YcN3z1XGabr2lvmI8"
#define STATUS_CHANGED_TEMPLATE_ID "4l8kO-X6ix5VLeQfPlQaq4g1GSKEW46q1ty6fB8plSk"
#else
#define APP_ID "wx089fad18302b2554"
#define APP_SECRET "739bb7e6a89c782c41e94802277a9831"
#define WARN_TEMPLATE_ID "mgTnSJsXptK-jGXiMqlU1KU5nsWnzHZndGnRKzhYjHE"
#define STATUS_CHANGED_TEMPLATE_ID "OsiC-JBJ2hnKPSgaj3ppMKv1FjuPKHEl_y9UZEUdWpI"
#define STATUS_CHANGED_TEMPLATE_ID_CLOSE                                       \
  "oGAjsrEhp_KBfaBV1nC1V3bqkAGIbPXBgJU73YLneUA"
#endif

#define MINIAPP_ID "wx089fad18302b2554"

#define WX_TOKEN_URL                                                           \
  "https://api.weixin.qq.com/cgi-bin/"                                         \
  "token?grant_type=client_credential&appid=%s&secret=%s"

#define WX_UNIONID_URL                                                         \
  "https://api.weixin.qq.com/cgi-bin/user/"                                    \
  "info?access_token=%s&openid=%s&lang=zh_CN"

#define WX_PUSH_URL                                                            \
  "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=%s"

using NJson = nlohmann::json;

std::mutex CWxHttpHandler::m_mutexWxToken;
std::string CWxHttpHandler::m_sWxToken = "";
HttpClientPool::Ptr CWxHttpHandler::m_pWxTokenClient = nullptr;
Poco::Timestamp CWxHttpHandler::m_tokenDT;

CWxHttpHandler::CWxHttpHandler() {}

void CWxHttpHandler::initWxTokenClient() {
  char sz[255] = {0};
  sprintf(sz, WX_TOKEN_URL, APP_ID, APP_SECRET);
  if (m_pWxTokenClient == nullptr) {
    m_pWxTokenClient = std::make_shared<HttpClientPool>(20, "GET", sz);
  }
}

void CWxHttpHandler::updateWxToken() {
  Poco::Timestamp now;

  // KCONSOLE_ERROR("dt: %lld", m_tokenDT.elapsed() - now.elapsed());

  // if (!m_sWxToken.empty() && m_tokenDT.elapsed() - now.elapsed() <
  //                                (uint64_t)1000 * 1000 * 60 * 60 * 2) {
  //   return;
  // }

  auto utc = m_pWxTokenClient->acquire();
  utc->_client->sendAsync(
      utc->_req, [utc](const koala::IKHttpResponse::Ptr &resp) {
        utc->_status = HTTP_STATUS_DONE;
        utc->_resp = resp;

        KCONSOLE_INFO(resp->getBody().c_str());
        if (resp->getStatusCode() == KHTTP_STATUS_OK) {
          std::lock_guard<std::mutex> locker(m_mutexWxToken);
          try {
            NJson jToken = NJson::parse(resp->getBody());
            m_sWxToken = jToken["access_token"].get<std::string>();
            m_tokenDT = Poco::Timestamp();
            // KCONSOLE_INFO("%lld: %s", m_tokenDT.elapsed(),
            // m_sWxToken.c_str());
          } catch (std::exception &e) {
            klog_error("update wx token failed, %s", e.what());
          }
        };

        m_pWxTokenClient->release(utc);
      });
}

void CWxHttpHandler::pushWarn(const NJson &jWarn) {
  try {
    char sz[255] = {0};
    sprintf(sz, WX_PUSH_URL, m_sWxToken.c_str());
    auto pWxPushClient = std::make_shared<HttpClientPool>(20, "POST", sz);

    NJson body;
    body["template_id"] = WARN_TEMPLATE_ID;
    body["topcolor"] = "#FF0000";
    body["url"] = jWarn["image"];

    NJson data;
#ifdef TestTemplate
    data["pos"] = jWarn["pos"];
    data["type"] = jWarn["type"];
    data["datetime"] = jWarn["DT"];
#else
    data["thing6"] = jWarn["pos"];
    data["const4"] = jWarn["type"];
    data["time3"] = jWarn["DT"];
#endif
    body["data"] = data;

    auto jOpenIds = jWarn["users"];

    auto utc = pWxPushClient->acquire();
    for (int i = 0; i < jOpenIds.size(); ++i) {
      body["touser"] = jOpenIds[i].get<std::string>();
      
      klog_info("wx push msg body:[%s]",body.dump().c_str());
      utc->_req->setBody(body.dump());
      utc->_resp = koala::KCreateHttpResponse();
      utc->_client->send(utc->_req, utc->_resp);

      klog_info("wx push resp: %s", utc->_resp->getBody().c_str());
    }
    pWxPushClient->release(utc);
  } catch (NJson::exception &e) {
    klog_error("CWxHttpHandler::pushWarn %s", e.what());
  }
}

void CWxHttpHandler::pushSocketStatusChanged(const NJson &jStatus) {
  char sz[255] = {0};
  sprintf(sz, WX_PUSH_URL, m_sWxToken.c_str());
  auto pWxPushClient = std::make_shared<HttpClientPool>(20, "POST", sz);

  NJson body;
  body["topcolor"] = "#FF0000";

  NJson data;
#ifdef TestTemplate
  data["pos"] = jStatus["pos"];
  data["status"] = jStatus["status"];
  data["DT"] = jStatus["DT"];
  data["msg"] = jStatus["msg"];
#else
  if (jStatus["status"]["value"].get<std::string>().find("寮€濮?") != -1) {
    body["template_id"] = STATUS_CHANGED_TEMPLATE_ID;
    data["character_string3"] = jStatus["dev"];
    data["thing9"] = jStatus["pos"];
    data["const36"] = jStatus["status"];
    data["time34"] = jStatus["DT"];
  } else {
    body["template_id"] = STATUS_CHANGED_TEMPLATE_ID_CLOSE;
    data["character_string3"] = jStatus["dev"];
    data["thing9"] = jStatus["pos"];
    data["const36"] = jStatus["status"];
    data["time12"] = jStatus["DT"];
  }
#endif

  body["data"] = data;

  auto jOpenIds = jStatus["users"];

  auto utc = pWxPushClient->acquire();
  for (int i = 0; i < jOpenIds.size(); ++i) {
    body["touser"] = jOpenIds[i].get<std::string>();
    utc->_req->setBody(body.dump());
    utc->_resp = koala::KCreateHttpResponse();
    utc->_client->send(utc->_req, utc->_resp);

    klog_info("wx push resp: %s", utc->_resp->getBody().c_str());
  }
  pWxPushClient->release(utc);
}

std::string CWxHttpHandler::getWxUnionId(const std::string &sOpenId) {
  char sz[255] = {0};
  sprintf(sz, WX_UNIONID_URL, m_sWxToken.c_str(), sOpenId.c_str());
  KCONSOLE_INFO(sz);

  auto resp =
      koala::KHttpsRequestGet(sz, "./cert/server.crt", "./cert/server.key");

  if (resp->getStatusCode() == KHTTP_STATUS_OK) {
    NJson jUnionId = NJson::parse(resp->getBody());
    if (jUnionId.find("unionid") != jUnionId.end()) {
      return jUnionId["unionid"].get<std::string>();
    }
  }

  return "";
}
