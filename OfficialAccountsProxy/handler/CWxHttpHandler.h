#include "http/HttpClientPool.hpp"
#include "net/http/IKHttpRouter.h"
#include <Poco/Timestamp.h>
#include <condition_variable>
#include <nlohmann/json.hpp>

using NJson = nlohmann::json;

class CWxHttpHandler {
public:
  CWxHttpHandler();

  static void initWxTokenClient();
  static void updateWxToken();

  void pushWarn(const NJson &jWarn);

  void pushSocketStatusChanged(const NJson &jStatus);

  std::string getWxUnionId(const std::string &sOpenId);

  static std::string wxToken() {
    std::lock_guard<std::mutex> locker(m_mutexWxToken);
    return m_sWxToken;
  }

private:
  static std::mutex m_mutexWxToken;
  static std::string m_sWxToken;
  static HttpClientPool::Ptr m_pWxTokenClient;
  static Poco::Timestamp m_tokenDT;
};