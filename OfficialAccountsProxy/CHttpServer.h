#pragma once
#include "CSington.h"
#include "core/IKApplication.h"
#include "handler/CWxHttpHandler.h"
#include "http/HttpClientPool.hpp"
#include "net/http/IKHttpRouter.h"
#include "net/http/IKHttpServer.h"
#include "nlohmann/json.hpp"
#include <mutex>

class CHttpServer {
public:
  CHttpServer();
  ~CHttpServer();

  void setApp(koala::IKApplication *pApp);

  void setEventLoop(koala::IKEventLoop::Ptr pLoop);

  void setModuleId(uint32_t nId);

  bool start();

  void stop();

  void sendPluginData(uint32_t nChannelId, const char *pData, int nLen);

  void dealResp(uint16_t nMsgId, const nlohmann::json &jObj);

  void reqAsync(int64_t nTimestamp, std::string &sProtocolUUID,
                const koala::IKHttpWriter::Ptr &writer);

  std::string wxToken() const;

private:
  void run();

  void onHttpModuleResp(const nlohmann::json &jObj);

  void onWarnPushReq(const nlohmann::json &jObj);

  void onSocketStatusPushReq(const nlohmann::json &jObj);

  static nlohmann::json getGarageInfo(const std::string &body);

  static nlohmann::json getGarageOrderUnionId(const std::string &body);

private:
  koala::IKHttpServer::Ptr m_pHttpSvr;
  koala::IKHttpRouter::Ptr m_pRouter;

  koala::IKApplication *m_pApp;
  koala::IKEventLoop::Ptr m_pLoop;
  uint32_t m_nId;

  std::mutex m_mutex;
  std::map<koala::IKHttpWriter::Ptr, std::pair<std::string, uint64_t>>
      m_mWriter;

  friend CSington<CHttpServer>;
};