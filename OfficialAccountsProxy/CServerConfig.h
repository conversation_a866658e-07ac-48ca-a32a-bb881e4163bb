
#include <string>
struct WX
{

    std::string APP_ID ;
    std::string APP_SECRET ;
    std::string WARN_TEMPLATE_ID ;
    std::string STATUS_CHANGED_TEMPLATE_ID ;
    std::string STATUS_CHANGED_TEMPLATE_ID_CLOSE ;
    std::string MINIAPP_ID ;
    std::string WX_TOKEN_URL ;
    std::string WX_UNIONID_URL ;
    std::string WX_PUSH_URL ;
};

struct DB
{

    std::string IP ;
    std::string DB ;
    std::string USER ;
    std::string PASS ;

};

struct GARAGE
{
    std::string GARAGE_ORDER_UNIONID_URL ;
    std::string GARAGE_INFO_URL ;
    std::string GARAGE_ORDER_UNIONID_URL_CONSUME ;
};



class CServerConfig
{
private:
WX m_wx;   
DB m_db;
GARAGE m_garage;
public:
    CServerConfig(/* args */);
    ~CServerConfig();
};

CServerConfig::CServerConfig(/* args */)
{
}

CServerConfig::~CServerConfig()
{
}
