#pragma once
#include <map>
#include <string>
#include <fstream>
#include <iostream>
#include "nlohmann/json.hpp"
#include "CSington.h"

using NJson = nlohmann::json;




class CServerConfig
{
private:
std::map<std::string,std::string> m_config;
public:
    // CServerConfig(/* args */);
    // ~CServerConfig();

    void init();
    std::string getConfig(const std::string& key) const;
    friend CSington<CServerConfig>;
};

// CServerConfig::CServerConfig(/* args */)
// {
// }

// CServerConfig::~CServerConfig()
// {
// }

void CServerConfig::init(){
    try {
        // 读取配置文件
        std::ifstream configFile("./etc/config.json");
        if (!configFile.is_open()) {
            std::cerr << "Error: Cannot open config.json file" << std::endl;
            return;
        }

        // 解析JSON
        NJson jsonConfig;
        configFile >> jsonConfig;
        configFile.close();

        // 将JSON数据存储到m_config map中
        for (auto& [key, value] : jsonConfig.items()) {
            if (value.is_string()) {
                m_config[key] = value.get<std::string>();
                // std::cout << "key: " << key<<"  ||||value:" << value.get<std::string>()<<std::endl;
            } else {
                // 对于非字符串类型，转换为字符串存储
                m_config[key] = value.dump();
            }
        }

        // std::cout << "Config loaded successfully. Total items: " << m_config.size() << std::endl;
        // std::string tkur = getConfig("WX_TOKEN_URL");
        // std::string appID = getConfig("APP_ID");
        // std::string appsecret = getConfig("APP_SECRET");
        // char sz[255] = {0};
        // sprintf(sz, tkur.c_str(), appID.c_str(), appsecret.c_str());
        // std::cout << "Config loaded successfully. Total items: " << sz << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "Error loading config: " << e.what() << std::endl;
    }
}

std::string CServerConfig::getConfig(const std::string& key) const {
    auto it = m_config.find(key);
    if (it != m_config.end()) {
        return it->second;
    }
    return ""; // 返回空字符串如果key不存在
}
