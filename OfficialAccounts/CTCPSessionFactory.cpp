#include "CTCPSessionFactory.h"

#include "CTCPSession.h"

CTCPSession *
CTCPSessionFactory::create(koala::IKApplication *_app, uint32_t _id,
                           std::shared_ptr<koala::IKEventLoop> _loop,
                           const koala::IKTcpMessage *pTcpMsg) const {
  CTCPSession *pSession = new CTCPSession(_app, _id, _loop);
  pSession->peerAddr(pTcpMsg->_peerAddr);
  pSession->sessionId(pTcpMsg->_session);
  return pSession;
}
