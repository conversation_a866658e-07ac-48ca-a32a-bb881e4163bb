#pragma once

#include "core/IKBaseService.h"
#include <map>
#include <mutex>

class CTCPSessionFactory;
class CTCPSession;
class COfficicalAccountsSvr : public koala::IKBaseService {
public:
  explicit COfficicalAccountsSvr(koala::IKApplication *app);
  virtual ~COfficicalAccountsSvr();

  // IKService
  virtual const char *name() const override;
  virtual bool initialize() override;
  virtual void uninitialize() override;

  // IQUeueSink
  virtual void onQueueMessage(koala::KQueueMessage::Ptr msg);

  void sendPluginData(uint32_t nChannelId, const char *pData, int nLen);

  void sendData2Garage(uint32_t nGarageId, const char *pData, int nLen);

  void sendDataByType(uint16_t nSvrType, const char *pData, int nLen);

  static void *findSession(const std::string &sUUID);
  static void insertUUID(const std::string &sUUID, void *pSession);
  static void eraseUUID(const std::string &sUUID);

  void closeSession(uint16_t nChannelId, int nSessionId);

private:
  void onTCPOnline(const koala::IKTcpMessage *pTCPMsg);

  void onTCPOffline(const koala::IKTcpMessage *pTCPMsg);

  void onTCPData(const koala::IKTcpMessage *pTCPMsg);

  void onHttpModuleData(int8_t *pData, uint32_t nLen);

private:
  bool initDB();

private:
  // TODO 需要记录p_uuid，并且对回复超时进行处理
  CTCPSessionFactory *m_pSessionFactory;
  std::map<int, CTCPSession *> m_mSession;

  static std::mutex m_mutex;
  static std::map<std::string, std::pair<int64_t, void *>>
      m_mReq; // <uuid, <timestamp, seesion*或者COfficicalAccountsSvr*>>
              // 请求记录
};