cmake_minimum_required(VERSION 3.16)

project(OfficialAccounts)

if(WIN32)
    set(LIBRARY_OUTPUT_PATH "${CMAKE_BINARY_DIR}/bin")
else()
    set(LIBRARY_OUTPUT_PATH "${CMAKE_BINARY_DIR}/../run/plugins")
endif()

aux_source_directory(. mainSrcCode)
aux_source_directory(./Handler handlerSrcCode)
aux_source_directory(./Data dataSrcCode)

add_library(OfficialAccounts SHARED ${mainSrcCode} ${handlerSrcCode} ${dataSrcCode})

target_link_libraries(OfficialAccounts koala PocoFoundation)