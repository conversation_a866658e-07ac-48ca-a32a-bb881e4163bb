#pragma once

#include "event/IKEvent.h"
#include <map>
#include <memory>
#include <stdint.h>
#include <string>

namespace koala {
class IKApplication;
class IKEventLoop;
class IMemoryPool;
} // namespace koala

class IHandler;
class CTCPSession {
public:
  CTCPSession(koala::IKApplication *_app, uint32_t _id,
              std::shared_ptr<koala::IKEventLoop> _loop);
  ~CTCPSession();

  void peerAddr(const char *pPeerAddr);
  std::string peerAddr() const;

  void sessionId(int nSessionId);
  int sessionId() const;

  std::map<uint16_t, std::pair<uint32_t, uint32_t>> sessionTypeInfo() const;

  void number(uint16_t nType, uint32_t nNum);
  uint32_t number(uint16_t nType);

  void dealMessage(const char *pData, int nLen);

  bool send(const char *pData, size_t nLen);

  static std::shared_ptr<koala::IMemoryPool> memoryPool();

private:
  void onHeart(const char *pData, int nLen);

  void onRegister(const char *pData, int nLen);

private:
  koala::IKApplication *m_pApp;                // 进程对象app
  uint32_t m_nModuleId;                        // 模块ID
  std::shared_ptr<koala::IKEventLoop> m_pLoop; // 事件loop
  koala::KTimerID m_nTimerId;                  // 定时器ID
  int m_nSessionId;                            // 链接ID
  std::string m_sPeerAddr;                     // Client地址
  char m_nBeat;                                // 心跳值
  int64_t m_nBeatDT;                           // 心跳时间戳
  std::map<uint16_t, IHandler *> m_mHandler;   // 业务handler
  std::map<uint16_t, std::pair<uint32_t, uint32_t>>
      m_mSessionTypeInfo; // <源ID，pair<编号，版本号>>
                          // 链接类型、一个链接可能会有多个类型，比如门禁、电磁阀板、总电开关等合并为一个板子
  static std::shared_ptr<koala::IMemoryPool> m_pMemPool; // 流操作内存池
};