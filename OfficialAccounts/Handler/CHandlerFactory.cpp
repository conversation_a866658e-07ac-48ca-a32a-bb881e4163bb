#include "CHandlerFactory.h"
#include "COfficicalAccountPushHandler.h"
#include "EServerType.h"

IHandler *CHandlerFactory::create(koala::IKApplication *_app,
                                  std::shared_ptr<koala::IKEventLoop> _loop,
                                  uint16_t nSrcId) {
  switch (nSrcId) {
  case emServerType::CloudWarn:
    return new COfficicalAccountPushHandler(_app, _loop);
  case emServerType::LocalConsume:
    return new COfficicalAccountPushHandler(_app, _loop);
  case emServerType::CloudConsume:
    return new COfficicalAccountPushHandler(_app, _loop);
  default:
    break;
  }
  return nullptr;
}
