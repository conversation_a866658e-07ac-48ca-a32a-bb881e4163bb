#pragma once
#include "CSington.h"
#include "nlohmann/json.hpp"
#include <memory>

namespace koala {
class IKApplication;
class IKEventLoop;
class IMemoryPool;
} // namespace koala

class COfficicalAccountsSvr;
class CHttpModuleHandler {
public:
  void setApp(koala::IKApplication *_app);

  void setLoop(std::shared_ptr<koala::IKEventLoop> _loop);

  void setWarnMgrSvr(COfficicalAccountsSvr *pSvr);

  void onAccountAddReq(const nlohmann::json &jObj);

  void onAccountDelReq(const nlohmann::json &jObj);

private:
  koala::IKApplication *m_pApp;
  std::shared_ptr<koala::IKEventLoop> m_pLoop;
  COfficicalAccountsSvr *m_pOfficicalAccountsSvr;
  static std::shared_ptr<koala::IMemoryPool> m_pMemPool; // 流操作内存池

  friend CSington<CHttpModuleHandler>;
};