#include "CHttpModuleHandler.h"
#include "../COfficicalAccountsSvr.h"
#include "../Data/COfficicalAccount.h"
#include "../Data/CWarnInfo.h"
#include "CommonFunc.h"
#include "EErrorCode.h"
#include "EServerType.h"
#include "NSMarco.h"
#include "ProtocolUtil.h"
#include "SDevControlReq.h"
#include "core/IKApplication.h"
#include <Poco/DateTimeFormatter.h>
#include <Poco/LocalDateTime.h>
#include <Poco/StringTokenizer.h>
#include "common/KLog.h"

#define MEM_POOL_BLOCK_SIZE 4096

using NJson = nlohmann::json;

koala::IMemoryPool::Ptr CHttpModuleHandler::m_pMemPool = nullptr;

void CHttpModuleHandler::setApp(koala::IKApplication *_app) {
  m_pApp = _app;
  if (m_pMemPool == nullptr) {
    m_pMemPool = m_pApp->getMemoryManager()->getMemoryPool(MEM_POOL_BLOCK_SIZE);
  }
}

void CHttpModuleHandler::setLoop(std::shared_ptr<koala::IKEventLoop> _loop) {
  m_pLoop = _loop;
}

void CHttpModuleHandler::setWarnMgrSvr(COfficicalAccountsSvr *pSvr) {
  m_pOfficicalAccountsSvr = pSvr;
}

void CHttpModuleHandler::onAccountAddReq(const nlohmann::json &jObj) {
  auto s = jObj.dump();

  auto DT = std::stoll(jObj["CreateTime"].get<std::string>()) * 1000;
  auto sOpenId = jObj["FromUserName"].get<std::string>();
  auto sUnionId = jObj["unionid"].get<std::string>();
  auto sProtocolUUID = jObj["p_uuid"].get<std::string>();
  
  COfficicalAccount account;
  account.setOpenId(sOpenId);
  account.setUnionId(sUnionId);
  account.setDT(DT);
  bool b = COfficicalAccountMgr::ins()->add(account);
  NJson jResp;
  jResp["p_uuid"] = sProtocolUUID;
  if (b) {
    jResp["msg"] = "success";
  } else {
    jResp["msg"] = "failed";
  }

  klog_info("onAccountAddReq OpenId:[%s]  UnionId:[%s]", sOpenId, sUnionId);

  std::string sResp = jResp.dump();
  m_pOfficicalAccountsSvr->sendPluginData(OFFICICAL_ACCOUNT_TCP_2_HTTP_CHANNEL,
                                          sResp.c_str(), sResp.length());
}

void CHttpModuleHandler::onAccountDelReq(const nlohmann::json &jObj) {
  auto sOpenId = jObj["FromUserName"].get<std::string>();
  auto sProtocolUUID = jObj["p_uuid"].get<std::string>();

  COfficicalAccount account;
  account.setOpenId(sOpenId);
  std::string sFilter = FIND_FILTER_STR_QUE(OpenId);
  bool b = COfficicalAccountMgr::ins()->del(account, sFilter);
  NJson jResp;
  jResp["p_uuid"] = sProtocolUUID;
  if (b) {
    jResp["msg"] = "success";
  } else {
    jResp["msg"] = "failed";
  }
  std::string sResp = jResp.dump();
  m_pOfficicalAccountsSvr->sendPluginData(OFFICICAL_ACCOUNT_TCP_2_HTTP_CHANNEL,
                                          sResp.c_str(), sResp.length());
}