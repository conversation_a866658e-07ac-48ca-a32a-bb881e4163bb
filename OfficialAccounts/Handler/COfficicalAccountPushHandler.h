#pragma once
#include "CHandler.h"

// 本地车库客户端业务处理类
// emServerType::LocalCloudGarage

class COfficicalAccountPushHandler : public CHandler {
public:
  COfficicalAccountPushHandler(koala::IKApplication *_app,
                               std::shared_ptr<koala::IKEventLoop> _loop);

  virtual void dealMessage(const char *pData, int nLen) override;

private:
  void deal(const char *pData, int nLen);

private:
};