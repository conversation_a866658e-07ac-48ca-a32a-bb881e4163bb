#pragma once

#include "IHandler.h"
#include <memory>

namespace koala {
class IKApplication;
class IKEventLoop;
} // namespace koala
class CHandler : public IHandler {
public:
  CHandler(koala::IKApplication *_app,
           std::shared_ptr<koala::IKEventLoop> _loop)
      : m_pApp(_app), m_pLoop(_loop), m_pSession(nullptr) {}

  virtual ~CHandler() {}

  virtual void setSession(CTCPSession *pSession) override {
    m_pSession = pSession;
  }

protected:
  koala::IKApplication *m_pApp;
  std::shared_ptr<koala::IKEventLoop> m_pLoop;
  CTCPSession *m_pSession;
};