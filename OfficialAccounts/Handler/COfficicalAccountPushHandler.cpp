#include "COfficicalAccountPushHandler.h"
#include "../COfficicalAccountsSvr.h"
#include "../Data/COfficicalAccount.h"
#include "NSMarco.h"
#include "ProtocolUtil.h"
#include "SOfficicalAccountPushReq.h"
#include "common/KLog.h"
#include "net/http/IKHttpClient.h"
#include "nlohmann/json.hpp"
#include <string>

using NJson = nlohmann::json;

extern COfficicalAccountsSvr *g_pOfficicalAccountsSvr;

#define GARAGE_ORDER_UNIONID_URL                                               \
  "https://192.168.1.37:8444/consume/package/active/query"

COfficicalAccountPushHandler::COfficicalAccountPushHandler(
    koala::IKApplication *_app, std::shared_ptr<koala::IKEventLoop> _loop)
    : CHandler(_app, _loop) {}

void COfficicalAccountPushHandler::dealMessage(const char *pData, int nLen) {
  SHead *pHead = (SHead *)pData;
  switch (pHead->m_nMsgId) {
  case SOfficicalAccountPushReq::MSG_ID:
    deal(pData, nLen);
    break;

  default:
    break;
  }
}

void COfficicalAccountPushHandler::deal(const char *pData, int nLen) {
  SOfficicalAccountPushReq info;
  try {
    protocolFromBytes(info, pData, nLen);
  } catch (std::exception &e) {
    klog_error("COfficicalAccountPushHandler::deal %s", e.what());
    return;
  }

  NJson jReq = NJson::parse(
      std::string((char *)info.m_vJson.data(), info.m_vJson.size()));

  if (!jReq.contains("msgId") || !jReq.contains("garage_id")) {
    klog_error("COfficicalAccountPushHandler::deal msgId not found or "
               "garage_id not found.");
    return;
  }

  auto nMsgId = jReq["msgId"].get<uint16_t>();
  if (nMsgId == OFFICICAL_ACCOUNT_WARN_PUSH_REQ) {
    NJson jBody;
    jBody["garage_id"] = jReq["garage_id"];
    auto resp =
        koala::KHttpsRequestPost(GARAGE_ORDER_UNIONID_URL, jBody.dump(),
                                 "./cert/server.crt", "./cert/server.key");

    if (resp->getStatusCode() == KHTTP_STATUS_OK) {
      klog_info(resp->getBody().c_str());
      try {
        NJson jUnionIds = NJson::parse(resp->getBody());
        auto jArr = jUnionIds["union_id"];
        std::string sUnionIds;
        for (int i = 0; i < jArr.size(); ++i) {
          sUnionIds += "'" + jArr[i].get<std::string>() + "'";
          if (i != jArr.size() - 1) {
            sUnionIds += ",";
          }
        }

        std::string sSql = "select * from OfficicalAccount where UnionId in (" +
                           sUnionIds + ")";
        klog_info("openid search: %s", sSql.c_str());
        
        auto lsObj = CDBTable::findObjsBySql(COfficicalAccountMgr::ins()->DB(),
                                             COfficicalAccount::parse, sSql);
        for (auto pObj : lsObj) {
          auto pAccount = dynamic_cast<COfficicalAccount *>(pObj);
          if (pAccount != nullptr) {
            jReq["open_ids"].push_back(pAccount->getOpenId());
          }
          delete pObj;
        }
      } catch (std::exception &e) {
        klog_error("COfficicalAccountPushHandler::deal "
                   "OFFICICAL_ACCOUNT_WARN_PUSH_REQ %s",
                   e.what());
        return;
      }
    }
  } else if (nMsgId == OFFICICAL_ACCOUNT_SOCKET_STATUS_PUSH_REQ) {
    auto sUnionId = jReq["union_id"].get<std::string>();
    COfficicalAccount account;
    account.setUnionId(sUnionId);
    std::string sFilter = FIND_FILTER_STR_QUE(UnionId);
    auto lsObj = COfficicalAccountMgr::ins()->findObjs(&account, sFilter);
    for (auto pObj : lsObj) {
      auto pAccount = dynamic_cast<COfficicalAccount *>(pObj);
      if (pAccount != nullptr) {
        jReq["open_ids"].push_back(pAccount->getOpenId());
      }
      delete pObj;
    }
  }

  // jReq["open_ids"].push_back("o8HLp69IimXNLBzguNjfdSOfm2Go");
  if (jReq["open_ids"].size() <= 0) {
    klog_error("open_ids count <= 0");
    return;
  }

  g_pOfficicalAccountsSvr->sendPluginData(OFFICICAL_ACCOUNT_TCP_2_HTTP_CHANNEL,
                                          jReq.dump().c_str(),
                                          jReq.dump().length());
}
