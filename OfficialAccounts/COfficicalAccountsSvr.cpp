#include "COfficicalAccountsSvr.h"
#include "CTCPSession.h"
#include "CTCPSessionFactory.h"
#include "CommonFunc.h"
#include "Data/CDataManager.h"
#include "Data/CMysqlDB.h"
#include "Data/COfficicalAccount.h"
#include "Data/CWarnInfo.h"
#include "EErrorCode.h"
#include "EServerType.h"
#include "Handler/CHttpModuleHandler.h"
#include "NSMarco.h"
#include "SHead.h"
#include "common/KLog.h"
#include "core/IKApplication.h"
#include "nlohmann/json.hpp"
#include <Poco/Timestamp.h>
#include <iomanip>
#include <iostream>

#define DB_PING_INTERVAL (60 * 60 * 1000)

using NJson = nlohmann::json;

COfficicalAccountsSvr *g_pOfficicalAccountsSvr = nullptr;
std::map<std::string, std::pair<int64_t, void *>> COfficicalAccountsSvr::m_mReq;

std::mutex COfficicalAccountsSvr::m_mutex;

COfficicalAccountsSvr::COfficicalAccountsSvr(koala::IKApplication *app)
    : koala::IKBaseService(app) {
  g_pOfficicalAccountsSvr = this;
  _loop->setSafeTimer(DB_PING_INTERVAL, [this](koala::KTimerID timerID) {
    auto pool = CSington<CMysqlDB>::ins()->DBPool();
    if (pool != nullptr) {
      pool->ping();
    }
  });
  
}

COfficicalAccountsSvr::~COfficicalAccountsSvr() {
  if (m_pSessionFactory != nullptr) {
    delete m_pSessionFactory;
    m_pSessionFactory = nullptr;
  }
}

const char *COfficicalAccountsSvr::name() const {
  return "OfficicalAccountsSvr";
}

bool COfficicalAccountsSvr::initialize() {
  _app->getQueueService()->subMessage(OFFICICAL_ACCOUNT_TCP_2_HTTP_CHANNEL,
                                      this);
  CSington<CHttpModuleHandler>::ins()->setApp(_app);
  CSington<CHttpModuleHandler>::ins()->setLoop(_loop);
  CSington<CHttpModuleHandler>::ins()->setWarnMgrSvr(this);

  m_pSessionFactory = new CTCPSessionFactory();
  auto b = initDB();
  return b;
}

void COfficicalAccountsSvr::uninitialize() {}

void COfficicalAccountsSvr::onQueueMessage(koala::KQueueMessage::Ptr msg) {
  if (msg->_msgType == KMSG_TYPE_TCP_SERVER) {
    auto tcpMsg = reinterpret_cast<koala::IKTcpMessage *>(msg->_msgData);
    if (tcpMsg->_msgType == KMSG_TCP_ONLINE) {
      onTCPOnline(tcpMsg);
    } else if (tcpMsg->_msgType == KMSG_TCP_OFFLINE) {
      onTCPOffline(tcpMsg);
    } else if (tcpMsg->_msgType == KMSG_TCP_DATA) {
      onTCPData(tcpMsg);
    }
  } else if (msg->_msgType == KMSG_TYPE_PLUGIN) {
    if (msg->_msgId ==
        OFFICICAL_ACCOUNT_TCP_2_HTTP_CHANNEL) { // http与tcp间通信
      onHttpModuleData(msg->_msgData, msg->_msgLen);
    }
  }
}

void COfficicalAccountsSvr::onTCPOnline(const koala::IKTcpMessage *pTCPMsg) {
  CTCPSession *pSession = m_pSessionFactory->create(_app, _id, _loop, pTCPMsg);
  if (pSession != nullptr) {
    auto itor = m_mSession.find(pTCPMsg->_session);
    if (itor != m_mSession.end()) {
      delete itor->second;
      m_mSession.erase(itor);
    }

    m_mSession[pTCPMsg->_session] = pSession;
  }
}

void COfficicalAccountsSvr::onTCPOffline(const koala::IKTcpMessage *pTCPMsg) {
  auto itor = m_mSession.find(pTCPMsg->_session);
  if (itor != m_mSession.end()) {
    delete itor->second;
    m_mSession.erase(itor);
  }
}

void COfficicalAccountsSvr::onTCPData(const koala::IKTcpMessage *pTCPMsg) {
  auto itor = m_mSession.find(pTCPMsg->_session);
  std::cout<<"COfficicalAccountsSvr::onTCPData" << std::endl;
  if (itor != m_mSession.end() && itor->second != nullptr) {
    itor->second->dealMessage(pTCPMsg->_pData, pTCPMsg->_dataLen);
  }
}

void COfficicalAccountsSvr::onHttpModuleData(int8_t *pData, uint32_t nLen) {
  std::string sJson((char *)pData, nLen);
  std::string sUUID = "";
  try {
    auto jReq = NJson::parse(sJson);
    sUUID = jReq["p_uuid"].get<std::string>();
    auto nMsgId = jReq["msgId"].get<uint16_t>();
    switch (nMsgId) {
    case OFFICICAL_ACCOUNT_ADD_REQ:
      CSington<CHttpModuleHandler>::ins()->onAccountAddReq(jReq);
      break;
    case OFFICICAL_ACCOUNT_DEL_REQ:
      CSington<CHttpModuleHandler>::ins()->onAccountDelReq(jReq);
      break;

    default:
      break;
    }

  } catch (NJson::exception &e) {
    NJson jResp;
    // jResp["msgId"] = PARAM_PARSE_RESULT;
    jResp["p_uuid"] = sUUID;
    jResp["errcode"] = ECODE_COMMON_FORMAT_ERR;
    jResp["errMsg"] = getErrorMessage(ECODE_COMMON_FORMAT_ERR);
    auto sResp = jResp.dump();
    sendPluginData(OFFICICAL_ACCOUNT_TCP_2_HTTP_CHANNEL, sResp.c_str(),
                   sResp.length());
  }
}

bool COfficicalAccountsSvr::initDB() {
  auto b = CSington<CMysqlDB>::ins()->init();
  if (b) {
    auto pool = CSington<CMysqlDB>::ins()->DBPool();
    COfficicalAccountMgr::ins()->setDB(pool);
    COfficicalAccountMgr::ins()->setTableName("OfficicalAccount");
    COfficicalAccountMgr::ins()->create();
  }

  return b;
}

void COfficicalAccountsSvr::sendPluginData(uint32_t nChannelId,
                                           const char *pData, int nLen) {
  auto buffer =
      _app->getMemoryManager()->alloc(sizeof(koala::KQueueMessage) + nLen);
  koala::KQueueMessage::Ptr pPluginMsg = koala::KCreateQueueMessage(buffer);
  memcpy(pPluginMsg->_msgData, pData, nLen);

  pPluginMsg->_msgLen = nLen;
  pPluginMsg->_srcId = _id;
  pPluginMsg->_msgType = KMSG_TYPE_PLUGIN;
  pPluginMsg->_msgId = nChannelId;
  _app->getQueueService()->send(pPluginMsg);
}

void COfficicalAccountsSvr::sendData2Garage(uint32_t nGarageId,
                                            const char *pData, int nLen) {
  for (auto itor : m_mSession) {
    if (itor.second == nullptr) {
      continue;
    }

    if (itor.second->number(emServerType::LocalGarage) == nGarageId) {
      itor.second->send(pData, nLen);
    }
  }
}

void COfficicalAccountsSvr::sendDataByType(uint16_t nSvrType, const char *pData,
                                           int nLen) {
  for (auto itor : m_mSession) {
    if (itor.second == nullptr) {
      continue;
    }

    auto mSessionInfo = itor.second->sessionTypeInfo();
    if (mSessionInfo.find(nSvrType) != mSessionInfo.end()) {
      itor.second->send(pData, nLen);
    }
  }
}

void *COfficicalAccountsSvr::findSession(const std::string &sUUID) {
  std::lock_guard<std::mutex> locker(m_mutex);
  auto itor = m_mReq.find(sUUID);
  if (itor != m_mReq.end()) {
    return itor->second.second;
  }

  return nullptr;
}

void COfficicalAccountsSvr::insertUUID(const std::string &sUUID,
                                       void *pSession) {
  std::lock_guard<std::mutex> locker(m_mutex);
  Poco::Timestamp now;
  m_mReq[sUUID] = std::make_pair(now.epochMicroseconds(), pSession);
}

void COfficicalAccountsSvr::eraseUUID(const std::string &sUUID) {
  std::lock_guard<std::mutex> locker(m_mutex);
  m_mReq.erase(sUUID);
}

void COfficicalAccountsSvr::closeSession(uint16_t nChannelId, int nSessionId) {
  auto buff = _app->getMemoryManager()->alloc(sizeof(koala::KQueueMessage) +
                                              sizeof(koala::IKTcpMessage));
  auto Sendmsg =
      KCreateTcpServerShutdownMessage(buff, _id, nChannelId, nSessionId);
  _app->getQueueService()->send(Sendmsg);
}

KSERVICE_EXPORT(COfficicalAccountsSvr)