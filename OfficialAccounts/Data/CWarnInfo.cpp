#include "CWarnInfo.h"
#include "SqlQuery.h"
#include "common/KLog.h"

CWarnInfo::CWarnInfo(uint32_t nId) : m_Id(nId) {}

CWarnInfo::~CWarnInfo() {}

void CWarnInfo::createPrepare(std::string &sPrepare) {
  char sz[1024] = {0};
  snprintf(
      sz, sizeof(sz), sPrepare.c_str(),
      "Id INTEGER PRIMARY KEY AUTO_INCREMENT, GUID VARCHAR(33), GarageId "
      "INT UNSIGNED, MonitorId INT UNSIGNED, BoardId INT UNSIGNED, CameraId "
      "INT UNSIGNED, Type INT UNSIGNED, FireNum INT UNSIGNED,"
      " X1 INT UNSIGNED, Y1 INT UNSIGNED,"
      " X2 INT UNSIGNED, Y2 INT UNSIGNED,"
      " X3 INT UNSIGNED, Y3 INT UNSIGNED,"
      " X4 INT UNSIGNED, Y4 INT UNSIGNED,"
      " X5 INT UNSIGNED, Y5 INT UNSIGNED,"
      " X6 INT UNSIGNED, Y6 INT UNSIGNED,"
      " X7 INT UNSIGNED, Y7 INT UNSIGNED,"
      " X8 INT UNSIGNED, Y8 INT UNSIGNED,"
      " DT BIGINT, ImgPath VARCHAR(255)");
  sPrepare = sz;
}

IDBObject *CWarnInfo::parse(const CSqlQuery &query) {
  try {
    auto nId = std::stoul(query.value("Id"));
    auto sGUID = query.value("GUID");
    auto nGarageId = std::stoul(query.value("GarageId"));
    auto nMonitorId = std::stoul(query.value("MonitorId"));
    auto nBoardId = std::stoul(query.value("BoardId"));
    auto nCameraId = std::stoul(query.value("CameraId"));
    auto nType = std::stoul(query.value("Type").c_str());
    auto nFireNum = std::stoul(query.value("FireNum").c_str());
    auto nX1 = std::stoul(query.value("X1"));
    auto nY1 = std::stoul(query.value("Y1"));
    auto nX2 = std::stoul(query.value("X2"));
    auto nY2 = std::stoul(query.value("Y2"));
    auto nX3 = std::stoul(query.value("X3"));
    auto nY3 = std::stoul(query.value("Y3"));
    auto nX4 = std::stoul(query.value("X4"));
    auto nY4 = std::stoul(query.value("Y4"));
    auto nX5 = std::stoul(query.value("X5"));
    auto nY5 = std::stoul(query.value("Y5"));
    auto nX6 = std::stoul(query.value("X6"));
    auto nY6 = std::stoul(query.value("Y6"));
    auto nX7 = std::stoul(query.value("X7"));
    auto nY7 = std::stoul(query.value("Y7"));
    auto nX8 = std::stoul(query.value("X8"));
    auto nY8 = std::stoul(query.value("Y8"));
    auto nDT = std::stoull(query.value("DT"));
    auto sImgPath = query.value("ImgPath");

    CWarnInfo *pWarn = new CWarnInfo(nId);
    pWarn->setGUID(sGUID);
    pWarn->setGarageId(nGarageId);
    pWarn->setMonitorId(nMonitorId);
    pWarn->setBoardId(nBoardId);
    pWarn->setCameraId(nCameraId);
    pWarn->setType(nType);
    pWarn->setFireNum(nFireNum);
    pWarn->setX1(nX1);
    pWarn->setY1(nY1);
    pWarn->setX2(nX2);
    pWarn->setY2(nY2);
    pWarn->setX3(nX3);
    pWarn->setY3(nY3);
    pWarn->setX4(nX4);
    pWarn->setY4(nY4);
    pWarn->setX5(nX5);
    pWarn->setY5(nY5);
    pWarn->setX6(nX6);
    pWarn->setY6(nY6);
    pWarn->setX7(nX7);
    pWarn->setY7(nY7);
    pWarn->setX8(nX8);
    pWarn->setY8(nY8);
    pWarn->setDT(nDT);
    pWarn->setImgPath(sImgPath);
    return pWarn;
  } catch (std::exception &e) {
    klog_error(e.what());
  }
  return nullptr;
}

void CWarnInfo::insertPrepare(CSqlQuery &query, std::string &sPrepare) const {
  char sz[1024] = {0};
  snprintf(
      sz, sizeof(sz), sPrepare.c_str(),
      "GUID, GarageId, MonitorId, BoardId, CameraId, Type, FireNum,"
      " X1, Y1, X2, Y2, X3, Y3, X4, Y4, X5, Y5, X6, Y6, X7, Y7, X8, Y8,"
      " DT, ImgPath",
      "':GUID', :GarageId, :MonitorId, :BoardId, :CameraId, :Type, :FireNum,"
      " :X1, :Y1, :X2, :Y2, :X3, :Y3, :X4, :Y4, :X5, :Y5, :X6, :Y6, :X7, "
      ":Y7, :X8, :Y8,"
      " :DT, ':ImgPath'");
  sPrepare = sz;

  query.prepare(sPrepare);
}

void CWarnInfo::updatePrepare(CSqlQuery &query, std::string &sPrepare) const {
  char sz[1024] = {0};
  snprintf(sz, sizeof(sz), sPrepare.c_str(),
           "GUID=':GUID', GarageId=:GarageId, MonitorId=:MonitorId, "
           "BoardId=:BoardId, CameraId=:CameraId, Type=:Type, FireNum=:FireNum,"
           " X1=:X1, Y1=:Y1, X2=:X2, Y2=:Y2, X3=:X3, Y3=:Y3, X4=:X4, Y4=:Y4, "
           "X5=:X5, Y5=:Y5, X6=:X6, Y6=:Y6, X7=:X7, Y7=:Y7, X8=:X8, Y8=:Y8,"
           " DT=:DT, ImgPath=':ImgPath' WHERE Id=:Id");
  sPrepare = sz;

  query.prepare(sPrepare);
}

void CWarnInfo::deletePrepare(CSqlQuery &query, std::string &sPrepare) const {
  query.prepare(sPrepare);
}

void CWarnInfo::findPrepare(CSqlQuery &query, std::string &sPrepare,
                            const std::string &sFilter) const {
  char sz[255] = {0};
  snprintf(sz, sizeof(sz), sPrepare.c_str(), "Id=:Id");
  sPrepare = sz;

  std::string s = "";
  if (sFilter.find(" ORDER BY") == 0) {
    s = sFilter;
  } else {
    s = std::string(" WHERE ") + sFilter;
  }
  sPrepare = sPrepare + s;
  query.prepare(sPrepare);
}

void CWarnInfo::bindValue(CSqlQuery &query) const {
  query.bindValue(":Id", m_Id);
  query.bindValue(":GUID", m_GUID);
  query.bindValue(":GarageId", m_GarageId);
  query.bindValue(":MonitorId", m_MonitorId);
  query.bindValue(":BoardId", m_BoardId);
  query.bindValue(":CameraId", m_CameraId);
  query.bindValue(":Type", m_Type);
  query.bindValue(":FireNum", m_FireNum);
  query.bindValue(":X1", m_X1);
  query.bindValue(":Y1", m_Y1);
  query.bindValue(":X2", m_X2);
  query.bindValue(":Y2", m_Y2);
  query.bindValue(":X3", m_X3);
  query.bindValue(":Y3", m_Y3);
  query.bindValue(":X4", m_X4);
  query.bindValue(":Y4", m_Y4);
  query.bindValue(":X5", m_X5);
  query.bindValue(":Y5", m_Y5);
  query.bindValue(":X6", m_X6);
  query.bindValue(":Y6", m_Y6);
  query.bindValue(":X7", m_X7);
  query.bindValue(":Y7", m_Y7);
  query.bindValue(":X8", m_X8);
  query.bindValue(":Y8", m_Y8);
  query.bindValue(":DT", m_DT);
  query.bindValue(":MaxDT", m_MaxDT);
  query.bindValue(":ImgPath", m_ImgPath);
}