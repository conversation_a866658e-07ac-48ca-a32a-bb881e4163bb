#pragma once

#include "CDataManager.h"
#include "DB/DBUtil.h"
#include "DB/IDBObject.h"
#include <stdint.h>
#include <string>

class COfficicalAccount : public IDBObject {
public:
  COfficicalAccount(uint32_t nId = 0, const std::string &sOpenId = "",
                    const std::string &sUnionId = "", int64_t nDT = 0);
  virtual ~COfficicalAccount();

  static void createPrepare(std::string &sPrepare);

  static IDBObject *parse(const CSqlQuery &query);

  virtual void insertPrepare(CSqlQuery &query,
                             std::string &sPrepare) const override;

  virtual void updatePrepare(CSqlQuery &query,
                             std::string &sPrepare) const override;

  virtual void deletePrepare(CSqlQuery &query,
                             std::string &sPrepare) const override;

  virtual void findPrepare(CSqlQuery &query, std::string &sPrepare,
                           const std::string &sFilter) const override;

  virtual void bindValue(CSqlQuery &query) const override;

  ADD_FIELD_READONLY(int, Id)
  ADD_FIELD(std::string, OpenId)
  ADD_FIELD(std::string, UnionId)
  ADD_FIELD(int64_t, DT)
};

using COfficicalAccountMgr = CSington<CDataManager<COfficicalAccount>>;