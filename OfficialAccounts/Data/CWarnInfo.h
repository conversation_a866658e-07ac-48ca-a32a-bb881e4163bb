#pragma once

#include "CDataManager.h"
#include "DB/DBUtil.h"
#include "DB/IDBObject.h"
#include <stdint.h>
#include <string>

class CWarnInfo : public IDBObject {
public:
  CWarnInfo(uint32_t nId = 0);
  virtual ~CWarnInfo();

  static void createPrepare(std::string &sPrepare);

  static IDBObject *parse(const CSqlQuery &query);

  virtual void insertPrepare(CSqlQuery &query,
                             std::string &sPrepare) const override;

  virtual void updatePrepare(CSqlQuery &query,
                             std::string &sPrepare) const override;

  virtual void deletePrepare(CSqlQuery &query,
                             std::string &sPrepare) const override;

  virtual void findPrepare(CSqlQuery &query, std::string &sPrepare,
                           const std::string &sFilter) const override;

  virtual void bindValue(CSqlQuery &query) const override;

  ADD_FIELD_READONLY(int, Id)
  ADD_FIELD(std::string, GUID)
  ADD_FIELD(uint32_t, GarageId)
  ADD_FIELD(uint8_t, MonitorId)
  ADD_FIELD(uint8_t, BoardId)
  ADD_FIELD(uint8_t, CameraId)
  ADD_FIELD(uint8_t, Type)
  ADD_FIELD(uint8_t, FireNum)
  ADD_FIELD(uint32_t, X1)
  ADD_FIELD(uint32_t, Y1) // 精度0.1米 取出后除以10
  ADD_FIELD(uint32_t, X2)
  ADD_FIELD(uint32_t, Y2) // 精度0.1米 取出后除以10
  ADD_FIELD(uint32_t, X3)
  ADD_FIELD(uint32_t, Y3) // 精度0.1米 取出后除以10
  ADD_FIELD(uint32_t, X4)
  ADD_FIELD(uint32_t, Y4) // 精度0.1米 取出后除以10
  ADD_FIELD(uint32_t, X5)
  ADD_FIELD(uint32_t, Y5) // 精度0.1米 取出后除以10
  ADD_FIELD(uint32_t, X6)
  ADD_FIELD(uint32_t, Y6) // 精度0.1米 取出后除以10
  ADD_FIELD(uint32_t, X7)
  ADD_FIELD(uint32_t, Y7) // 精度0.1米 取出后除以10
  ADD_FIELD(uint32_t, X8)
  ADD_FIELD(uint32_t, Y8) // 精度0.1米 取出后除以10
  ADD_FIELD(int64_t, DT)
  ADD_FIELD(int64_t, MaxDT)       // 用于查询筛选
  ADD_FIELD(std::string, ImgPath) // 报警图片路径
};

using CWarnInfoMgr = CSington<CDataManager<CWarnInfo, 5>>;