#include "DBTable.h"
#include "CDBHelper.h"
#include "SqlQuery.h"
#include "common/KLog.h"
#include <iostream>

CDBTable::CDBTable(const std::string &sName) : m_pDB(nullptr) {
  m_sTbName = sName;
}

void CDBTable::setDB(koala::IKDataBasePool::Ptr pDB) { m_pDB = pDB; }

void CDBTable::setName(const std::string &sName) { m_sTbName = sName; }

bool CDBTable::create(CreateFunc func) {
  if (m_pDB == nullptr) {
    return false;
  }

  CDBHelper helper(m_pDB);
  auto session = helper.getConn();
  if (session == nullptr) {
    return false;
  }
  CSqlQuery query(session);
  std::string sPrepare =
      std::string("CREATE TABLE ") + m_sTbName + std::string("( %s )");
  func(sPrepare);
  query.prepare(sPrepare);
  bool b = query.exec();
  return b;
}

bool CDBTable::save(const IDBObject &obj) {
  if (m_pDB == nullptr) {
    return false;
  }

  CDBHelper helper(m_pDB);
  auto session = helper.getConn();
  if (session == nullptr) {
    return false;
  }

  CSqlQuery query(session);
  std::string sPrepare =
      std::string("INSERT INTO ") + m_sTbName + std::string("(%s) VALUES(%s)");
  obj.insertPrepare(query, sPrepare);
  obj.bindValue(query);
  query.prepare(sPrepare);
  bool b = query.exec();
  return b;
}

bool CDBTable::replace(const IDBObject &obj) {
  if (m_pDB == nullptr) {
    return false;
  }

  CDBHelper helper(m_pDB);
  auto session = helper.getConn();
  if (session == nullptr) {
    return false;
  }

  CSqlQuery query(session);
  std::string sPrepare =
      std::string("UPDATE ") + m_sTbName + " SET " + std::string("%s");
  obj.updatePrepare(query, sPrepare);
  obj.bindValue(query);
  query.prepare(sPrepare);
  bool b = query.exec();
  return b;
}

bool CDBTable::del(const IDBObject &obj, const std::string &sFilter) {
  if (m_pDB == nullptr) {
    return false;
  }

  CDBHelper helper(m_pDB);
  auto session = helper.getConn();
  if (session == nullptr) {
    return false;
  }

  CSqlQuery query(session);
  std::string sPrepare =
      std::string("DELETE FROM ") + m_sTbName + " WHERE " + sFilter;
  obj.deletePrepare(query, sPrepare);
  obj.bindValue(query);
  query.prepare(sPrepare);
  bool b = query.exec();
  return b;
}

int CDBTable::count(IDBObject *pObj, const std::string &sFilter) const {
  int nCount = 0;
  if (m_pDB == nullptr) {
    return nCount;
  }

  CDBHelper helper(m_pDB);
  auto session = helper.getConn();
  if (session == nullptr) {
    return nCount;
  }

  CSqlQuery query(session);
  std::string sPrepare =
      std::string("SELECT count(*) AS count FROM ") + m_sTbName;
  if (pObj != nullptr && !sFilter.empty()) {
    pObj->findPrepare(query, sPrepare, sFilter);
    pObj->bindValue(query);
  }
  query.prepare(sPrepare);

  bool b = query.exec();
  if (b) {
    nCount = atoi(query.value("count").c_str());
  }
  return nCount;
}

std::list<IDBObject *> CDBTable::find(ParseFunc func, const IDBObject *pObj,
                                      const std::string &sFilter, int nBegin,
                                      int nCount) {
  std::list<IDBObject *> lsObject;
  if (m_pDB == nullptr) {
    return lsObject;
  }

  CDBHelper helper(m_pDB);
  auto session = helper.getConn();
  if (session == nullptr) {
    return lsObject;
  }

  CSqlQuery query(session);
  std::string sPrepare = std::string("SELECT * FROM ") + m_sTbName;

  char sz[255] = {0};
  if (nBegin != -1 && nCount != -1) {
    snprintf(sz, sizeof(sz), " LIMIT %d, %d ", nBegin, nCount);
  }

  std::string sLimit = sz;
  if (pObj != nullptr && !sFilter.empty()) {
    pObj->findPrepare(query, sPrepare, sFilter);
    pObj->bindValue(query);
  }

  query.prepare(sPrepare.append(sLimit));
  bool b = query.exec();
  if (b) {
    bool bMore = query.getResult();
    while (bMore) {
      lsObject.push_back(func(query));
      bMore = query.next();
    }
  }

  return lsObject;
}

std::list<IDBObject *> CDBTable::findObjsBySql(ParseFunc func,
                                               const IDBObject *pObj,
                                               std::string &sSql) {
  std::list<IDBObject *> lsObject;
  if (m_pDB == nullptr) {
    return lsObject;
  }

  CDBHelper helper(m_pDB);
  auto session = helper.getConn();
  if (session == nullptr) {
    return lsObject;
  }

  CSqlQuery query(session);
  std::string sPrepare = std::string("SELECT * FROM ") + m_sTbName;

  if (pObj != nullptr) {
    pObj->bindValue(query);
  }

  query.prepare(sSql);
  bool b = query.exec();
  if (b) {
    bool bMore = true;
    do {
      lsObject.push_back(func(query));
      bMore = query.next();
    } while (bMore);
  }

  return lsObject;
}

std::list<IDBObject *> CDBTable::findObjsBySql(koala::IKDataBasePool::Ptr pDB,
                                               ParseFunc func,
                                               std::string &sSql) {
  std::list<IDBObject *> lsObject;
  if (pDB == nullptr) {
    return lsObject;
  }

  CDBHelper helper(pDB);
  auto session = helper.getConn();
  if (session == nullptr) {
    return lsObject;
  }

  CSqlQuery query(session);
  query.prepare(sSql);
  bool b = query.exec();
  if (b && query.getResult()) {
    bool bMore = true;
    do {
      lsObject.push_back(func(query));
      bMore = query.next();
    } while (bMore);
  }

  return lsObject;
}