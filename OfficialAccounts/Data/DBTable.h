#pragma once

#include "DB/IDBTable.h"
#include <database/IKDataBasePool.h>
#include <memory.h>
#include <string>

class CDBTable : public IDBTable {
public:
  CDBTable(const std::string &sName = "");

  virtual void setDB(koala::IKDataBasePool::Ptr pDB) override;

  virtual void setName(const std::string &sName) override;

  virtual bool create(CreateFunc func) override;

  virtual bool save(const IDBObject &obj) override;

  virtual bool replace(const IDBObject &obj) override;

  virtual bool del(const IDBObject &obj,
                   const std::string &sFilter = "Id = :Id") override;

  virtual int count(IDBObject *pObj, const std::string &sFilter) const override;

  virtual std::list<IDBObject *> find(ParseFunc func, const IDBObject *pObj,
                                      const std::string &sFilter,
                                      int nBegin = -1,
                                      int nCount = -1) override;

  virtual std::list<IDBObject *> findObjsBySql(ParseFunc func,
                                               const IDBObject *pObj,
                                               std::string &sSql) override;

  static std::list<IDBObject *> findObjsBySql(koala::IKDataBasePool::Ptr pDB,
                                              ParseFunc func,
                                              std::string &sSql);

private:
  std::string m_sTbName;

  koala::IKDataBasePool::Ptr m_pDB;
};