#include "SqlQuery.h"

CSqlQuery::CSqlQuery(koala::IKDatabase::Ptr conn) : m_pConn(conn) {
  m_current = m_resule.begin();
}

CSqlQuery::CSqlQuery(const CSqlQuery &other)
    : m_pConn(other.m_pConn), m_mData(other.m_mData), m_sSql(other.m_sSql),
      m_resule(other.m_resule) {
  m_current = m_resule.begin();
}

CSqlQuery::~CSqlQuery() {}

void CSqlQuery::prepare(std::string &sPrepare) {
  for (auto itor = m_mData.cbegin(); itor != m_mData.cend(); ++itor) {
    std::string::size_type pos = 0;
    std::string::size_type nSrcLen = itor->first.length();
    std::string::size_type nDesLen = itor->second.length();
    while (true) {
      pos = sPrepare.find(itor->first, pos);
      if (pos == std::string::npos) {
        break;
      }
      sPrepare = sPrepare.replace(pos, nSrcLen, itor->second);
      pos += nDesLen;
    }
  }

  m_sSql = sPrepare;
}

bool CSqlQuery::getResult() {
  if (m_resule.size() > 0) {
    m_current = m_resule.begin();
    return true;
  }
  return false;
}

bool CSqlQuery::exec() {
  if (m_sSql.empty()) {
    return false;
  }

  m_resule = m_pConn->query(m_sSql);

  return true;
}

bool CSqlQuery::next() {
  if (m_current == m_resule.end()) {
    return false;
  }

  ++m_current;
  if (m_current == m_resule.end()) {
    return false;
  }

  return true;
}

std::string CSqlQuery::value(const std::string &sFiled) const {
  if (m_current == m_resule.end()) {
    return "";
  }

  return (*m_current).at(sFiled);
}
