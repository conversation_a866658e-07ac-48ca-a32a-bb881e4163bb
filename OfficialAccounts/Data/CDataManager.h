#pragma once

#include "CSington.h"
#include "ConsistentHash.h"
#include "DB/IDBObject.h"
#include "DB/IDBTable.h"
#include "DB/IDataManager.h"
#include "DBTable.h"
#include "NSMarco.h"
#include <list>
#include <memory>

template <class T, int virtualNod = 10>
class CDataManager : public IDataManager {
public:
  CDataManager()
      : m_pTable(nullptr), m_pDB(nullptr),
        m_pHash(new ConsistentHash(virtualNod)) {}
  virtual ~CDataManager() { SAFE_DELETE(m_pTable); }

  ConsistentHash::Ptr hash() const { return m_pHash; }

  virtual void setDB(koala::IKDataBasePool::Ptr pDB) override { m_pDB = pDB; }

  virtual koala::IKDataBasePool::Ptr DB() override { return m_pDB; }

  virtual koala::IKDataBasePool::Ptr database() const { return m_pDB; }

  virtual void setTableName(const std::string &sTBName) override {
    if (m_pDB == nullptr) {
      return;
    }

    if (m_pTable == nullptr) {
      m_pTable = new CDBTable();
      m_pTable->setDB(m_pDB);
    }
    m_pTable->setName(sTBName);
  }

  virtual bool create() override {
    if (m_pTable == nullptr)
      return false;

    return m_pTable->create(T::createPrepare);
  }

  virtual bool add(IDBObject &obj) override { return m_pTable->save(obj); }

  virtual bool replace(IDBObject &obj) override {
    return m_pTable->replace(obj);
  }

  virtual bool del(IDBObject &obj,
                   const std::string &sFilter = "Id = :Id") override {
    return m_pTable->del(obj, sFilter);
  }

  virtual int count(IDBObject *pObj,
                    const std::string &sFilter) const override {
    return m_pTable->count(pObj, sFilter);
  }

  virtual std::list<IDBObject *> findObjs(const IDBObject *pObj,
                                          const std::string &sFilter,
                                          int nBegin = -1,
                                          int nCount = -1) const override {
    return m_pTable->find(T::parse, pObj, sFilter, nBegin, nCount);
  }

  virtual std::list<IDBObject *>
  findObjsBySql(const IDBObject *pObj, std::string &sSql) const override {
    return m_pTable->findObjsBySql(T::parse, pObj, sSql);
  }

protected:
  IDBTable *m_pTable;
  koala::IKDataBasePool::Ptr m_pDB;
  ConsistentHash::Ptr m_pHash;

  friend CSington<CDataManager<T>>;
};