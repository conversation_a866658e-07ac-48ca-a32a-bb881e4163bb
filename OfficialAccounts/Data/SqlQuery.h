#pragma once

#include "Value.h"
#include "database/IKDataBase.h"
#include <map>
#include <memory>
#include <string>

class CSqlQuery {
public:
  CSqlQuery(koala::IKDatabase::Ptr conn);
  CSqlQuery(const CSqlQuery &other);
  ~CSqlQuery();

  void prepare(std::string &s);

  bool getResult();

  bool exec();

  bool next();

  std::string value(const std::string &sFiled) const;

  template <class T> void bindValue(std::string sFlag, T value) {
    CValue<T> v(value);
    m_mData[sFlag] = v.toString();
  }

private:
  std::string m_sSql;
  std::map<std::string, std::string> m_mData;
  koala::IKDatabase::Ptr m_pConn;
  koala::KQueryResult m_resule;
  koala::KQueryResult::iterator m_current;
};
