#include "COfficicalAccount.h"
#include "SqlQuery.h"
#include "common/KLog.h"

COfficicalAccount::COfficicalAccount(uint32_t nId, const std::string &sOpenId,
                                     const std::string &sUnionId, int64_t nDT)
    : m_Id(nId), m_OpenId(sOpenId), m_UnionId(sUnionId), m_DT(nDT) {}

COfficicalAccount::~COfficicalAccount() {}

void COfficicalAccount::createPrepare(std::string &sPrepare) {
  char sz[1024] = {0};
  snprintf(sz, sizeof(sz), sPrepare.c_str(),
           "Id INTEGER PRIMARY KEY AUTO_INCREMENT, OpenId VARCHAR(29) UNIQUE, "
           "UnionId VARCHAR(29) UNIQUE, DT BIGINT");
  sPrepare = sz;
}

IDBObject *COfficicalAccount::parse(const CSqlQuery &query) {
  try {
    auto nId = std::stoul(query.value("Id"));
    auto sOpenId = query.value("OpenId");
    auto sUnionId = query.value("UnionId");
    auto nDT = std::stoll(query.value("DT"));

    COfficicalAccount *pAccount =
        new COfficicalAccount(nId, sOpenId, sUnionId, nDT);
    return pAccount;
  } catch (std::exception &e) {
    klog_error(e.what());
  }
  return nullptr;
}

void COfficicalAccount::insertPrepare(CSqlQuery &query,
                                      std::string &sPrepare) const {
  char sz[1024] = {0};
  snprintf(sz, sizeof(sz), sPrepare.c_str(), "OpenId, UnionId, DT",
           "':OpenId', ':UnionId', :DT");
  sPrepare = sz;

  query.prepare(sPrepare);
}

void COfficicalAccount::updatePrepare(CSqlQuery &query,
                                      std::string &sPrepare) const {
  char sz[255] = {0};
  snprintf(sz, sizeof(sz), sPrepare.c_str(),
           "OpenId=:OpenId, UnionId=:UnionId, DT=:DT WHERE Id=:Id");
  sPrepare = sz;

  query.prepare(sPrepare);
}

void COfficicalAccount::deletePrepare(CSqlQuery &query,
                                      std::string &sPrepare) const {
  query.prepare(sPrepare);
}

void COfficicalAccount::findPrepare(CSqlQuery &query, std::string &sPrepare,
                                    const std::string &sFilter) const {
  char sz[255] = {0};
  snprintf(sz, sizeof(sz), sPrepare.c_str(), "Id=:Id");
  sPrepare = sz;

  std::string s = "";
  if (sFilter.find(" ORDER BY") == 0) {
    s = sFilter;
  } else {
    s = std::string(" WHERE ") + sFilter;
  }
  sPrepare = sPrepare + s;
  query.prepare(sPrepare);
}

void COfficicalAccount::bindValue(CSqlQuery &query) const {
  query.bindValue(":Id", m_Id);
  query.bindValue(":OpenId", m_OpenId);
  query.bindValue(":UnionId", m_UnionId);
  query.bindValue(":DT", m_DT);
}