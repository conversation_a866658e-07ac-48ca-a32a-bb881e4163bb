#include "CMysqlDB.h"
#include "common/KLog.h"
#include "database/IKDataBasePool.h"
#include "event/IKEventLoop.h"
#include <iostream>
#include <thread>

CMysqlDB::CMysqlDB() {}

CMysqlDB::~CMysqlDB() {}

bool CMysqlDB::init() {
  m_pool = koala::KCreateDataBasePool();
  bool b =
      m_pool->initialize("tcp://192.168.1.247:3306", "test", "root", "111111");
  if (b) {
    klog_info("init database success!");
  } else {
    klog_error("init database failed!");
  }

  return b;
}

koala::IKDataBasePool::Ptr CMysqlDB::DBPool() const { return m_pool; }
