#include "CMysqlDB.h"
#include "common/KLog.h"
#include "database/IKDataBasePool.h"
#include "event/IKEventLoop.h"
#include <iostream>
#include <thread>
#include "../../OfficialAccountsProxy/CServerConfig.h"

CMysqlDB::CMysqlDB() {}

CMysqlDB::~CMysqlDB() {}

bool CMysqlDB::init() {
  m_pool = koala::KCreateDataBasePool();
  std::string strIP = CSington<CServerConfig>::ins()->getConfig("IP");
  std::string strDB = CSington<CServerConfig>::ins()->getConfig("DB");
  std::string strUser = CSington<CServerConfig>::ins()->getConfig("USER");
  std::string strPass = CSington<CServerConfig>::ins()->getConfig("PASS");
  bool b =
      m_pool->initialize(strIP.c_str(), strDB.c_str(), strUser.c_str(), strPass.c_str());
  if (b) {
    klog_info("init database success!");
  } else {
    klog_error("init database failed!");
  }

  return b;
}

koala::IKDataBasePool::Ptr CMysqlDB::DBPool() const { return m_pool; }
