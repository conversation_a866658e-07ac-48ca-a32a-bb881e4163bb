#include "CTCPSession.h"
#include "COfficicalAccountsSvr.h"
#include "CommonFunc.h"
#include "EErrorCode.h"
#include "EServerType.h"
#include "Handler/CHandlerFactory.h"
#include "Handler/IHandler.h"
#include "ProtocolUtil.h"
#include "SHead.h"
#include "SHeart.h"
#include "SRegister.h"
#include "SRegisterResp.h"
#include "SUUIDGarageInfoQuery.h"
#include "SUUIDGarageInfoResp.h"
#include "common/KLog.h"
#include "core/IKApplication.h"
#include <Poco/Timestamp.h>
#include <iostream>

#define OFFICICAL_ACCOUNT_SVR_CHANNEL 0x1106
#define MEM_POOL_BLOCK_SIZE 4096

#define HEART_TIME_OUT (30 * 1000 * 1000)

koala::IMemoryPool::Ptr CTCPSession::m_pMemPool = nullptr;
extern COfficicalAccountsSvr *g_pOfficicalAccountsSvr;

CTCPSession::CTCPSession(koala::IKApplication *_app, uint32_t _id,
                         std::shared_ptr<koala::IKEventLoop> _loop)
    : m_pApp(_app), m_nModuleId(_id), m_pLoop(_loop) {
  if (m_pMemPool == nullptr) {
    m_pMemPool = m_pApp->getMemoryManager()->getMemoryPool(MEM_POOL_BLOCK_SIZE);
  }
  m_nBeatDT = Poco::Timestamp().epochMicroseconds();

  m_nTimerId = m_pLoop->setInterval(1000, [this](koala::KTimerID timerID) {
    Poco::Timestamp now;
    if (now.epochMicroseconds() - m_nBeatDT > HEART_TIME_OUT) {
      g_pOfficicalAccountsSvr->closeSession(OFFICICAL_ACCOUNT_SVR_CHANNEL,
                                            m_nSessionId);
    }
  });
}

CTCPSession::~CTCPSession() {
  for (auto itor = m_mHandler.begin(); itor != m_mHandler.end(); ++itor) {
    delete itor->second;
  }

  m_mHandler.clear();

  m_pLoop->killTimer(m_nTimerId);
}

void CTCPSession::dealMessage(const char *pData, int nLen) {
  SHead *pHead = (SHead *)pData;
  switch (pHead->m_nMsgId) {
  case SHeart::MSG_ID:
    onHeart(pData, nLen);
    std::cout<<"CTCPSession::dealMessage SHeart::MSG_ID" << std::endl;
    break;

  case SRegister::MSG_ID:
    onRegister(pData, nLen);
    std::cout<<"CTCPSession::dealMessage SRegister::MSG_ID" << std::endl;
    break;

  default:
    for (auto itor : m_mHandler) {
      itor.second->dealMessage(pData, nLen);
    }
    // if (!m_mHandler.empty()) {
    //   auto itor = m_mHandler.find(pHead->m_nSrcId);
    //   if (itor != m_mHandler.end() && itor->second != nullptr) {
    //     itor->second->dealMessage(pData, nLen);
    //   }
    // }
    break;
  }

  std::string sType = "";
  for (auto itor : m_mHandler) {
    if (!sType.empty()) {
      sType += ",";
    }

    char szType[7] = {0};
    sprintf(szType, "0x%04X", itor.first);
    sType += szType;
  }
  klog_info("recv msgId: 0x%04X from sessionId: %d type: %s", pHead->m_nMsgId,
            m_nSessionId, sType.c_str());
}

bool CTCPSession::send(const char *pData, size_t nLen) {
  auto buffer = m_pApp->getMemoryManager()->alloc(
      sizeof(koala::KQueueMessage) + sizeof(koala::IKTcpMessage) + nLen);
  auto respMsg = koala::KCreateTcpMessageWithData(
      buffer, KMSG_TCP_DATA, m_nModuleId, OFFICICAL_ACCOUNT_SVR_CHANNEL,
      KMSG_TYPE_TCP_SERVER, m_nSessionId, pData, nLen);
  m_pApp->getQueueService()->send(respMsg);
  return false;
}

koala::IMemoryPool::Ptr CTCPSession::memoryPool() { return m_pMemPool; }

void CTCPSession::onHeart(const char *pData, int nLen) {
  SHeart info;
  try {
    protocolFromBytes(info, pData, nLen);
  } catch (std::exception &e) {
    klog_error("CTCPSession::onHeart %s", e.what());
    return;
  }

  Poco::Timestamp now;
  m_nBeatDT = now.epochMicroseconds();
  m_nBeat = info.m_nBeat + 1;

  KMemoryAllocator<char> allocator(m_pMemPool);
  std::vector<char, KMemoryAllocator<char>> vData(allocator);
  protocolToBytes(info, &vData);
  send(vData.data(), vData.size());
}

// 车库未创建时，num为0，待注册后进行更新
void CTCPSession::onRegister(const char *pData, int nLen) {
  SRegister info;
  try {
    protocolFromBytes(info, pData, nLen);
  } catch (std::exception &e) {
    klog_error("CTCPSession::onRegister %s", e.what());
    return;
  }
  SRegisterResp resp;
  resp.m_head.m_nDestId = info.m_head.m_nSrcId;
  resp.m_head.m_nSrcId = emServerType::OfficicalAccounts;
  memcpy(resp.m_head.m_uniqueId, info.m_head.m_uniqueId,
         sizeof(info.m_head.m_uniqueId));
  resp.m_nCode = ECODE_COMMON_SUC;
  auto itor = m_mHandler.find(info.m_head.m_nSrcId);
  if (itor != m_mHandler.end()) {
    resp.m_nCode = ECODE_COMMON_REG_NUM_REPEAT;
  } else {
    m_mSessionTypeInfo[info.m_head.m_nSrcId] =
        std::make_pair(info.m_nNum, info.m_nVersion);

    auto pHandler = CSington<CHandlerFactory>::ins()->create(
        m_pApp, m_pLoop, info.m_head.m_nSrcId);
    if (pHandler) {
      pHandler->setSession(this);
      m_mHandler[info.m_head.m_nSrcId] = pHandler;
    }
  }

  KMemoryAllocator<char> allocator(m_pMemPool);
  std::vector<char, KMemoryAllocator<char>> vData(allocator);
  protocolToBytes(resp, &vData);
  send(vData.data(), vData.size());
}

void CTCPSession::peerAddr(const char *pPeerAddr) { m_sPeerAddr = pPeerAddr; }

std::string CTCPSession::peerAddr() const { return m_sPeerAddr; }

void CTCPSession::sessionId(int nSessionId) { m_nSessionId = nSessionId; }

int CTCPSession::sessionId() const { return m_nSessionId; }

std::map<uint16_t, std::pair<uint32_t, uint32_t>>
CTCPSession::sessionTypeInfo() const {
  return m_mSessionTypeInfo;
}

void CTCPSession::number(uint16_t nType, uint32_t nNum) {
  auto &pair = m_mSessionTypeInfo[nType];
  pair.first = nNum;
}

uint32_t CTCPSession::number(uint16_t nType) {
  if (m_mSessionTypeInfo.find(nType) != m_mSessionTypeInfo.end()) {
    auto pair = m_mSessionTypeInfo.at(nType);
    return pair.first;
  }
  return 0;
}
